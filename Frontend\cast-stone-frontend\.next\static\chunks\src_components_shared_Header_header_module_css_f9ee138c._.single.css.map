{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/shared/Header/header.module.css"], "sourcesContent": ["/* Magazine/Editorial Theme Variables - Navy Blue Theme */\n\n/* Header Styles */\n.header {\n  background-color: var(--cast-stone-white);\n  border-bottom: 1px solid rgba(30, 58, 138, 0.08);\n  padding: 0;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  box-shadow: 0 2px 8px var(--cast-stone-shadow);\n  backdrop-filter: blur(10px);\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 80px;\n}\n\n/* Logo Styles */\n.logo {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.logoLink {\n  text-decoration: none;\n  color: var(--cast-stone-navy);\n  transition: var(--transition-smooth);\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.logoLink:hover {\n  transform: translateY(-1px);\n}\n\n.logoText {\n  font-size: 2rem;\n  font-weight: 700;\n  letter-spacing: -0.02em;\n  line-height: 1;\n  font-family: 'Georgia', 'Times New Roman', serif;\n}\n\n.logoSubtext {\n  font-size: 0.75rem;\n  font-weight: 400;\n  letter-spacing: 0.1em;\n  text-transform: uppercase;\n  color: var(--cast-stone-navy-gray);\n  margin-top: 2px;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n}\n\n/* Navigation Styles */\n.nav {\n  display: flex;\n  align-items: center;\n}\n\n.navList {\n  display: flex;\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  gap: 0;\n  align-items: center;\n}\n\n.navItem {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.navLink,\n.navButton {\n  text-decoration: none;\n  color: var(--cast-stone-navy);\n  font-weight: 500;\n  font-size: 0.95rem;\n  padding: 1.5rem 1.25rem;\n  transition: var(--transition-smooth);\n  position: relative;\n  background: none;\n  border: none;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  letter-spacing: 0.01em;\n}\n\n.navLink:hover,\n.navButton:hover {\n  color: var(--cast-stone-light-navy);\n  transform: translateY(-1px);\n}\n\n.navLink::after,\n.navButton::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  width: 0;\n  height: 2px;\n  background: linear-gradient(90deg, var(--cast-stone-navy), var(--cast-stone-light-navy));\n  transition: var(--transition-smooth);\n  transform: translateX(-50%);\n}\n\n.navLink:hover::after,\n.navButton:hover::after,\n.navButton.active::after {\n  width: 80%;\n}\n\n/* Dropdown Styles */\n.dropdownContainer {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.dropdownIcon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: var(--transition-smooth);\n  color: var(--cast-stone-navy-gray);\n}\n\n.dropdownIcon.rotated {\n  transform: rotate(180deg);\n  color: var(--cast-stone-navy);\n}\n\n.loadingIcon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--cast-stone-navy-gray);\n}\n\n.dropdown {\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  transform: translateX(-50%);\n  background: var(--cast-stone-white);\n  border: 1px solid rgba(74, 55, 40, 0.1);\n  border-radius: 8px;\n  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);\n  min-width: 220px;\n  z-index: 1001;\n  opacity: 0;\n  visibility: hidden;\n  transform: translateX(-50%) translateY(-10px);\n  transition: var(--transition-smooth);\n  animation: dropdownSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;\n  backdrop-filter: blur(10px);\n}\n\n@keyframes dropdownSlideIn {\n  from {\n    opacity: 0;\n    visibility: hidden;\n    transform: translateX(-50%) translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    visibility: visible;\n    transform: translateX(-50%) translateY(0);\n  }\n}\n\n.dropdownList {\n  list-style: none;\n  margin: 0;\n  padding: 0.5rem 0;\n}\n\n.dropdownItem {\n  position: relative;\n}\n\n.dropdownLink {\n  display: block;\n  padding: 0.75rem 1.25rem;\n  color: var(--cast-stone-navy);\n  text-decoration: none;\n  font-size: 0.9rem;\n  font-weight: 400;\n  transition: var(--transition-fast);\n  border-left: 3px solid transparent;\n}\n\n.dropdownLink:hover {\n  background: rgba(30, 58, 138, 0.04);\n  color: var(--cast-stone-light-navy);\n  border-left-color: var(--cast-stone-navy);\n  transform: translateX(2px);\n}\n\n/* Sub-dropdown Styles */\n.subDropdownList {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  background: rgba(74, 55, 40, 0.02);\n  border-top: 1px solid rgba(74, 55, 40, 0.08);\n}\n\n.subDropdownItem {\n  position: relative;\n}\n\n.subDropdownLink {\n  display: block;\n  padding: 0.6rem 1.25rem 0.6rem 2rem;\n  color: var(--cast-stone-navy-gray);\n  text-decoration: none;\n  font-size: 0.85rem;\n  font-weight: 400;\n  transition: var(--transition-fast);\n  border-left: 3px solid transparent;\n  position: relative;\n}\n\n.subDropdownLink::before {\n  content: '→';\n  position: absolute;\n  left: 1.5rem;\n  color: var(--cast-stone-navy-gray);\n  font-size: 0.7rem;\n  transition: var(--transition-fast);\n}\n\n.subDropdownLink:hover {\n  background: rgba(30, 58, 138, 0.06);\n  color: var(--cast-stone-navy);\n  border-left-color: var(--cast-stone-light-navy);\n  transform: translateX(2px);\n}\n\n.subDropdownLink:hover::before {\n  color: var(--cast-stone-navy);\n  transform: translateX(2px);\n}\n\n/* Cart Styles */\n.cartContainer {\n  display: flex;\n  align-items: center;\n  margin-left: 1rem;\n}\n\n.cartLink {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 44px;\n  height: 44px;\n  color: var(--cast-stone-navy);\n  text-decoration: none;\n  border-radius: 8px;\n  transition: var(--transition-smooth);\n  position: relative;\n}\n\n.cartLink:hover {\n  background: rgba(30, 58, 138, 0.06);\n  color: var(--cast-stone-light-navy);\n  transform: translateY(-1px);\n}\n\n.cartIconWrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.cartBadge {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  background: #dc2626;\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 600;\n  min-width: 20px;\n  height: 20px;\n  border-radius: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  animation: cartBadgeAppear 0.3s ease-out;\n}\n\n@keyframes cartBadgeAppear {\n  0% {\n    transform: scale(0);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1.2);\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .container {\n    padding: 0 1.5rem;\n  }\n\n  .navList {\n    gap: 0;\n  }\n\n  .navLink,\n  .navButton {\n    padding: 1.5rem 1rem;\n    font-size: 0.9rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .container {\n    padding: 0 1rem;\n    height: 70px;\n  }\n\n  .logoText {\n    font-size: 1.75rem;\n  }\n\n  .logoSubtext {\n    font-size: 0.7rem;\n  }\n\n  .navList {\n    gap: 0;\n  }\n\n  .navLink,\n  .navButton {\n    padding: 1.25rem 0.75rem;\n    font-size: 0.85rem;\n  }\n\n  .dropdown {\n    min-width: 200px;\n  }\n}\n\n@media (max-width: 640px) {\n  .nav {\n    display: none; /* Will implement mobile menu in future */\n  }\n\n  .container {\n    justify-content: space-between;\n  }\n}\n"], "names": [], "mappings": "AAGA;;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;;AASA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;;;;;;;;AAmBA;;;;;AAMA;;;;;;;;;;;;AAaA;;;;AAOA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;AAQA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;;;;AAeA;EACE;;;;EAIA;;;;EAIA;;;;;;AAOF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA"}}]}