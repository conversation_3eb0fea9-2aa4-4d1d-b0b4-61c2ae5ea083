{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductCard/productCard.module.css"], "sourcesContent": ["/* Product Card Styles - Magazine/Editorial Theme */\n.productCard {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.productCard:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n/* Image Container */\n.imageContainer {\n  position: relative;\n  width: 100%;\n  height: 250px;\n  overflow: hidden;\n}\n\n.productImage {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.productCard:hover .productImage {\n  transform: scale(1.05);\n}\n\n.outOfStockOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n/* Product Info */\n.productInfo {\n  padding: 1.5rem;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.productName {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0 0 0.75rem 0;\n  line-height: 1.3;\n}\n\n.productDescription {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  line-height: 1.5;\n  margin: 0 0 1rem 0;\n  flex: 1;\n}\n\n/* Price Container */\n.priceContainer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.price {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.collection {\n  color: #8b7355;\n  font-size: 0.85rem;\n  font-style: italic;\n}\n\n/* Stock Info */\n.stockInfo {\n  margin-bottom: 1rem;\n}\n\n.inStock {\n  color: #059669;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n.outOfStock {\n  color: #dc2626;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n/* Action Buttons */\n.actionButtons {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n  margin-top: auto;\n}\n\n.viewDetailsBtn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.75rem 1rem;\n  background: transparent;\n  color: #8b7355;\n  border: 2px solid #8b7355;\n  border-radius: 4px;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.viewDetailsBtn:hover {\n  background: #8b7355;\n  color: white;\n}\n\n/* Add to Cart Section */\n.addToCartSection {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.quantitySelector {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.quantityBtn {\n  width: 32px;\n  height: 32px;\n  border: 1px solid #d1d5db;\n  background: white;\n  color: #4a3728;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.quantityBtn:hover:not(:disabled) {\n  background: #f3f4f6;\n  border-color: #8b7355;\n}\n\n.quantityBtn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.quantity {\n  min-width: 40px;\n  text-align: center;\n  font-weight: 600;\n  color: #4a3728;\n}\n\n.addToCartBtn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 0.875rem 1rem;\n  background: #8b7355;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-weight: 600;\n  font-size: 0.9rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.addToCartBtn:hover:not(:disabled) {\n  background: #6d5a47;\n  transform: translateY(-1px);\n}\n\n.addToCartBtn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.cartIcon {\n  width: 18px;\n  height: 18px;\n  stroke-width: 2;\n}\n\n.loading {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.loading::after {\n  content: '';\n  width: 16px;\n  height: 16px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .productCard {\n    margin-bottom: 1rem;\n  }\n  \n  .imageContainer {\n    height: 200px;\n  }\n  \n  .productInfo {\n    padding: 1rem;\n  }\n  \n  .productName {\n    font-size: 1.1rem;\n  }\n  \n  .price {\n    font-size: 1.25rem;\n  }\n  \n  .actionButtons {\n    gap: 0.5rem;\n  }\n  \n  .addToCartSection {\n    flex-direction: column;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAgBA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAOA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}