{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/checkout/checkout.module.css"], "sourcesContent": ["/* Checkout Page Styles - Magazine/Editorial Theme */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n  min-height: 80vh;\n}\n\n/* Header */\n.header {\n  text-align: center;\n  margin-bottom: 3rem;\n  padding-bottom: 2rem;\n  border-bottom: 2px solid #f3f4f6;\n}\n\n.title {\n  color: #4a3728;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 2rem 0;\n  line-height: 1.2;\n}\n\n/* Step Indicator */\n.stepIndicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.step span {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: #e5e7eb;\n  color: #6b7280;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.step.active span {\n  background: #8b7355;\n  color: white;\n}\n\n.step label {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  font-weight: 600;\n}\n\n.step.active label {\n  color: #4a3728;\n}\n\n.stepConnector {\n  width: 60px;\n  height: 2px;\n  background: #e5e7eb;\n}\n\n/* Checkout Content */\n.checkoutContent {\n  display: grid;\n  grid-template-columns: 1fr 400px;\n  gap: 3rem;\n}\n\n/* Main Content */\n.mainContent {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n}\n\n.sectionTitle {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 2rem 0;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n/* Form Styles */\n.formGrid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.formGroup {\n  display: flex;\n  flex-direction: column;\n}\n\n.formGroup.fullWidth {\n  grid-column: 1 / -1;\n}\n\n.formGroup label {\n  color: #4a3728;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n}\n\n.formGroup input,\n.formGroup select {\n  padding: 0.75rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease;\n}\n\n.formGroup input:focus,\n.formGroup select:focus {\n  outline: none;\n  border-color: #8b7355;\n}\n\n/* Payment Methods */\n.paymentMethods {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.paymentMethod {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.5rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.paymentMethod:hover {\n  border-color: #8b7355;\n}\n\n.paymentMethod.selected {\n  border-color: #8b7355;\n  background: #f8f7f5;\n}\n\n.paymentIcon {\n  font-size: 2rem;\n  width: 60px;\n  text-align: center;\n}\n\n.paymentInfo {\n  flex: 1;\n}\n\n.paymentInfo h3 {\n  color: #4a3728;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin: 0 0 0.25rem 0;\n}\n\n.paymentInfo p {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  margin: 0;\n}\n\n.radioButton input {\n  width: 20px;\n  height: 20px;\n  accent-color: #8b7355;\n}\n\n/* Step Actions */\n.stepActions {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  padding-top: 2rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.nextBtn,\n.placeOrderBtn {\n  padding: 1rem 2rem;\n  background: #8b7355;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.nextBtn:hover,\n.placeOrderBtn:hover:not(:disabled) {\n  background: #6d5a47;\n  transform: translateY(-2px);\n}\n\n.placeOrderBtn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.backBtn {\n  padding: 1rem 2rem;\n  background: transparent;\n  color: #6b5b4d;\n  border: 2px solid #e5e7eb;\n  border-radius: 6px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.backBtn:hover {\n  border-color: #8b7355;\n  color: #8b7355;\n}\n\n/* Order Summary */\n.orderSummary {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  height: fit-content;\n  position: sticky;\n  top: 2rem;\n}\n\n.summaryTitle {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 1.5rem 0;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n/* Order Items */\n.orderItems {\n  margin-bottom: 1.5rem;\n}\n\n.orderItem {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem 0;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.orderItem:last-child {\n  border-bottom: none;\n}\n\n.itemImage {\n  width: 60px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 6px;\n}\n\n.itemDetails {\n  flex: 1;\n  min-width: 0;\n}\n\n.itemDetails h4 {\n  color: #4a3728;\n  font-size: 0.9rem;\n  font-weight: 600;\n  margin: 0 0 0.25rem 0;\n  line-height: 1.3;\n}\n\n.itemDetails p {\n  color: #6b5b4d;\n  font-size: 0.8rem;\n  margin: 0;\n}\n\n.itemPrice {\n  color: #4a3728;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n/* Summary Totals */\n.summaryTotals {\n  padding-top: 1rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.summaryRow {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.summaryRow span:first-child {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n}\n\n.summaryRow span:last-child {\n  color: #4a3728;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.totalRow {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 0;\n  border-top: 2px solid #4a3728;\n  margin-top: 1rem;\n}\n\n.totalRow span:first-child {\n  color: #4a3728;\n  font-size: 1.1rem;\n  font-weight: 700;\n}\n\n.totalRow span:last-child {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .checkoutContent {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .orderSummary {\n    position: static;\n    order: -1;\n  }\n}\n\n@media (max-width: 768px) {\n  .container {\n    padding: 1rem;\n  }\n\n  .title {\n    font-size: 2rem;\n  }\n\n  .stepIndicator {\n    gap: 0.5rem;\n  }\n\n  .step span {\n    width: 35px;\n    height: 35px;\n    font-size: 0.9rem;\n  }\n\n  .stepConnector {\n    width: 40px;\n  }\n\n  .mainContent,\n  .orderSummary {\n    padding: 1.5rem;\n  }\n\n  .formGrid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .stepActions {\n    flex-direction: column-reverse;\n  }\n\n  .nextBtn,\n  .placeOrderBtn,\n  .backBtn {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0.5rem;\n  }\n\n  .title {\n    font-size: 1.75rem;\n  }\n\n  .mainContent,\n  .orderSummary {\n    padding: 1rem;\n  }\n\n  .sectionTitle {\n    font-size: 1.25rem;\n  }\n\n  .paymentMethod {\n    padding: 1rem;\n  }\n\n  .paymentIcon {\n    font-size: 1.5rem;\n    width: 40px;\n  }\n\n  .orderItem {\n    flex-direction: column;\n    align-items: flex-start;\n    text-align: center;\n  }\n\n  .itemImage {\n    align-self: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;AAOA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;;AASA;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;AAaA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;EACE;;;;;EAKA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;;AAQF;EACE;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA", "debugId": null}}]}