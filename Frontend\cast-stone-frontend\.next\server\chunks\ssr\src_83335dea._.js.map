{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/admin/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAdminAuth } from '@/contexts/AdminAuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading } = useAdminAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/admin/login');\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-white\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto\"></div>\n          <p className=\"mt-4 text-amber-900\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null; // Will redirect to login\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;;;;;;;;;;;;IAI3C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,yBAAyB;IACxC;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { useAdminAuth } from '@/contexts/AdminAuthContext';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function AdminLayout({ children }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n  const { admin, logout } = useAdminAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = () => {\n    logout();\n    router.push('/admin/login');\n  };\n\n  const navigation = [\n    {\n      name: 'Dashboard',\n      href: '/admin/dashboard',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Collections',\n      href: '/admin/dashboard/collections',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Products',\n      href: '/admin/dashboard/products',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Orders',\n      href: '/admin/dashboard/orders',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-amber-50\">\n      {/* Sidebar */}\n      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 border-r border-amber-200`}>\n        <div className=\"flex items-center justify-center h-16 px-4 bg-amber-900\">\n          <h1 className=\"text-xl font-bold text-white\">Cast Stone Admin</h1>\n        </div>\n\n        <nav className=\"mt-8\">\n          <div className=\"px-4 space-y-2\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <a\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-amber-100 text-amber-900 border-r-2 border-amber-900'\n                      : 'text-amber-800 hover:bg-amber-50 hover:text-amber-900'\n                  }`}\n                >\n                  {item.icon}\n                  <span className=\"ml-3\">{item.name}</span>\n                </a>\n              );\n            })}\n          </div>\n        </nav>\n      </div>\n\n      {/* Main content */}\n      <div className={`lg:pl-64 flex flex-col flex-1`}>\n        {/* Top header */}\n        <header className=\"bg-white shadow-sm border-b border-amber-200\">\n          <div className=\"flex items-center justify-between px-6 py-4\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(!sidebarOpen)}\n                className=\"lg:hidden p-2 rounded-md text-amber-600 hover:text-amber-800 hover:bg-amber-50\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n              <h2 className=\"ml-4 text-2xl font-bold text-amber-900\">\n                {navigation.find(item => item.href === pathname)?.name || 'Dashboard'}\n              </h2>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-sm text-amber-800\">\n                Welcome, <span className=\"font-semibold text-amber-900\">{admin?.email}</span>\n              </div>\n              <button\n                onClick={handleLogout}\n                className=\"px-4 py-2 text-sm font-medium text-white bg-amber-900 rounded-md hover:bg-amber-800 transition-colors shadow-sm\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Page content */}\n        <main className=\"flex-1 p-6 bg-amber-50\">\n          {children}\n        </main>\n      </div>\n\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-amber-900 bg-opacity-75 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,cAAc,kBAAkB,oBAAoB,8GAA8G,CAAC;;kCAClP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA+B;;;;;;;;;;;kCAG/C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,4DACA,yDACJ;;wCAED,KAAK,IAAI;sDACV,8OAAC;4CAAK,WAAU;sDAAQ,KAAK,IAAI;;;;;;;mCAT5B,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAW,CAAC,6BAA6B,CAAC;;kCAE7C,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe,CAAC;4CAC/B,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDACX,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,QAAQ;;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAyB;8DAC7B,8OAAC;oDAAK,WAAU;8DAAgC,OAAO;;;;;;;;;;;;sDAElE,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;YAKJ,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/types/entities.ts"], "sourcesContent": ["// Collection Types\nexport interface Collection {\n  id: number;\n  name: string;\n  description?: string;\n  level: number;\n  parentCollectionId?: number;\n  childCollectionId?: number;\n  tags: string[];\n  published: boolean;\n  createdBy: string;\n  createdAt: string;\n  updatedBy?: string;\n  updatedAt?: string;\n  parentCollection?: Collection;\n  childCollection?: Collection;\n  products: Product[];\n}\n\nexport interface CollectionHierarchy {\n  id: number;\n  name: string;\n  description?: string;\n  level: number;\n  tags: string[];\n  published: boolean;\n  children: CollectionHierarchy[];\n  productCount: number;\n}\n\nexport interface CreateCollectionRequest {\n  name: string;\n  description?: string;\n  level: number;\n  parentCollectionId?: number;\n  childCollectionId?: number;\n  tags: string[];\n  published: boolean;\n  createdBy: string;\n}\n\nexport interface UpdateCollectionRequest {\n  name: string;\n  description?: string;\n  level: number;\n  parentCollectionId?: number;\n  childCollectionId?: number;\n  tags: string[];\n  published: boolean;\n  updatedBy: string;\n}\n\nexport interface CollectionFilterRequest {\n  name?: string;\n  level?: number;\n  parentCollectionId?: number;\n  published?: boolean;\n  createdBy?: string;\n  createdAfter?: string;\n  createdBefore?: string;\n  updatedAfter?: string;\n  updatedBefore?: string;\n  tag?: string;\n  pageNumber?: number;\n  pageSize?: number;\n  sortBy?: string;\n  sortDirection?: 'asc' | 'desc';\n}\n\n// Product Types\nexport interface Product {\n  id: number;\n  name: string;\n  description?: string;\n  price: number;\n  stock: number;\n  collectionId: number;\n  images: string[];\n  tags: string[];\n  createdAt: string;\n  updatedAt?: string;\n  collection?: Collection;\n}\n\nexport interface ProductSummary {\n  id: number;\n  name: string;\n  price: number;\n  stock: number;\n  mainImage?: string;\n  collectionName: string;\n  inStock: boolean;\n}\n\nexport interface CreateProductRequest {\n  name: string;\n  description?: string;\n  price: number;\n  stock: number;\n  collectionId: number;\n  images: string[];\n  tags: string[];\n}\n\nexport interface UpdateProductRequest {\n  name: string;\n  description?: string;\n  price: number;\n  stock: number;\n  collectionId: number;\n  images: string[];\n  tags: string[];\n}\n\nexport interface ProductFilterRequest {\n  name?: string;\n  collectionId?: number;\n  minPrice?: number;\n  maxPrice?: number;\n  minStock?: number;\n  maxStock?: number;\n  inStock?: boolean;\n  createdAfter?: string;\n  createdBefore?: string;\n  updatedAfter?: string;\n  updatedBefore?: string;\n  tag?: string;\n  pageNumber?: number;\n  pageSize?: number;\n  sortBy?: string;\n  sortDirection?: 'asc' | 'desc';\n}\n\n// Order Types\nexport interface Order {\n  id: number;\n  userId?: number;\n  email: string;\n  phoneNumber?: string;\n  country?: string;\n  city?: string;\n  zipCode?: string;\n  totalAmount: number;\n  statusId: number;\n  paymentMethod?: string;\n  createdAt: string;\n  user?: User;\n  status: Status;\n  orderItems: OrderItem[];\n}\n\nexport interface OrderSummary {\n  id: number;\n  email: string;\n  totalAmount: number;\n  statusName: string;\n  createdAt: string;\n  itemCount: number;\n}\n\nexport interface OrderItem {\n  id: number;\n  productId: number;\n  quantity: number;\n  priceAtPurchaseTime: number;\n  orderId: number;\n  product?: Product;\n}\n\nexport interface CreateOrderRequest {\n  userId?: number;\n  email: string;\n  phoneNumber?: string;\n  country?: string;\n  city?: string;\n  zipCode?: string;\n  paymentMethod?: string;\n  orderItems: CreateOrderItemRequest[];\n}\n\nexport interface CreateOrderItemRequest {\n  productId: number;\n  quantity: number;\n}\n\nexport interface UpdateOrderStatusRequest {\n  statusId: number;\n}\n\nexport interface OrderFilterRequest {\n  userId?: number;\n  email?: string;\n  statusId?: number;\n  minAmount?: number;\n  maxAmount?: number;\n  paymentMethod?: string;\n  country?: string;\n  city?: string;\n  createdAfter?: string;\n  createdBefore?: string;\n  pageNumber?: number;\n  pageSize?: number;\n  sortBy?: string;\n  sortDirection?: 'asc' | 'desc';\n}\n\n// User Types\nexport interface User {\n  id: number;\n  role: string;\n  email: string;\n  phoneNumber?: string;\n  name?: string;\n  country?: string;\n  city?: string;\n  zipCode?: string;\n  createdAt: string;\n  active: boolean;\n}\n\nexport interface CreateUserRequest {\n  role: string;\n  email: string;\n  phoneNumber?: string;\n  password: string;\n  name?: string;\n  country?: string;\n  city?: string;\n  zipCode?: string;\n  active: boolean;\n}\n\nexport interface UpdateUserRequest {\n  role: string;\n  phoneNumber?: string;\n  name?: string;\n  country?: string;\n  city?: string;\n  zipCode?: string;\n  active: boolean;\n}\n\nexport interface UserFilterRequest {\n  email?: string;\n  role?: string;\n  active?: boolean;\n  country?: string;\n  city?: string;\n  name?: string;\n  createdAfter?: string;\n  createdBefore?: string;\n  pageNumber?: number;\n  pageSize?: number;\n  sortBy?: string;\n  sortDirection?: 'asc' | 'desc';\n}\n\n// Status Types\nexport interface Status {\n  id: number;\n  statusName: string;\n}\n"], "names": [], "mappings": "AAAA,mBAAmB", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/get.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { \n  Product, \n  ProductSummary, \n  ProductFilterRequest,\n  PaginatedResponse \n} from '../../types/entities';\n\nexport class ProductGetService extends BaseService {\n  /**\n   * Get all products\n   */\n  async getAll(): Promise<Product[]> {\n    this.logApiCall('GET', ApiEndpoints.Products.Base);\n    return this.handleResponse(\n      this.client.get<Product[]>(ApiEndpoints.Products.Base)\n    );\n  }\n\n  /**\n   * Get product by ID\n   */\n  async getById(id: number): Promise<Product> {\n    this.logApiCall('GET', ApiEndpoints.Products.ById(id));\n    return this.handleResponse(\n      this.client.get<Product>(ApiEndpoints.Products.ById(id))\n    );\n  }\n\n  /**\n   * Get products by collection ID\n   */\n  async getByCollection(collectionId: number): Promise<Product[]> {\n    this.logApiCall('GET', ApiEndpoints.Products.ByCollection(collectionId));\n    return this.handleResponse(\n      this.client.get<Product[]>(ApiEndpoints.Products.ByCollection(collectionId))\n    );\n  }\n\n  /**\n   * Get products in stock\n   */\n  async getInStock(): Promise<Product[]> {\n    this.logApiCall('GET', ApiEndpoints.Products.InStock);\n    return this.handleResponse(\n      this.client.get<Product[]>(ApiEndpoints.Products.InStock)\n    );\n  }\n\n  /**\n   * Get featured products\n   */\n  async getFeatured(count: number = 10): Promise<ProductSummary[]> {\n    this.logApiCall('GET', ApiEndpoints.Products.Featured, { count });\n    return this.handleResponse(\n      this.client.get<ProductSummary[]>(ApiEndpoints.Products.Featured, { count })\n    );\n  }\n\n  /**\n   * Get latest products\n   */\n  async getLatest(count: number = 10): Promise<ProductSummary[]> {\n    this.logApiCall('GET', ApiEndpoints.Products.Latest, { count });\n    return this.handleResponse(\n      this.client.get<ProductSummary[]>(ApiEndpoints.Products.Latest, { count })\n    );\n  }\n\n  /**\n   * Search products by name\n   */\n  async search(name: string): Promise<Product[]> {\n    this.logApiCall('GET', ApiEndpoints.Products.Search, { name });\n    return this.handleResponse(\n      this.client.get<Product[]>(ApiEndpoints.Products.Search, { name })\n    );\n  }\n\n  /**\n   * Get products by price range\n   */\n  async getByPriceRange(minPrice: number, maxPrice: number): Promise<Product[]> {\n    this.logApiCall('GET', ApiEndpoints.Products.PriceRange, { minPrice, maxPrice });\n    return this.handleResponse(\n      this.client.get<Product[]>(ApiEndpoints.Products.PriceRange, { minPrice, maxPrice })\n    );\n  }\n\n  /**\n   * Get products with advanced filtering and pagination\n   */\n  async getFiltered(filters: ProductFilterRequest): Promise<PaginatedResponse<Product>> {\n    const cleanFilters = ServiceUtils.cleanObject(filters);\n    this.logApiCall('GET', ApiEndpoints.Products.Filter, cleanFilters);\n    \n    return this.handlePaginatedResponse(\n      this.client.get<PaginatedResponse<Product>>(\n        ApiEndpoints.Products.Filter, \n        cleanFilters\n      )\n    );\n  }\n\n  /**\n   * Get products with default pagination\n   */\n  async getPaginated(\n    pageNumber: number = 1, \n    pageSize: number = 10,\n    sortBy: string = 'createdAt',\n    sortDirection: 'asc' | 'desc' = 'desc'\n  ): Promise<PaginatedResponse<Product>> {\n    const filters: ProductFilterRequest = {\n      pageNumber,\n      pageSize,\n      sortBy,\n      sortDirection\n    };\n    \n    return this.getFiltered(filters);\n  }\n\n  /**\n   * Get products by tag\n   */\n  async getByTag(tag: string): Promise<Product[]> {\n    const filters: ProductFilterRequest = {\n      tag,\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get low stock products\n   */\n  async getLowStock(threshold: number = 10): Promise<Product[]> {\n    const filters: ProductFilterRequest = {\n      minStock: 1,\n      maxStock: threshold,\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get out of stock products\n   */\n  async getOutOfStock(): Promise<Product[]> {\n    const filters: ProductFilterRequest = {\n      inStock: false,\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get products in price range with pagination\n   */\n  async getByPriceRangePaginated(\n    minPrice: number, \n    maxPrice: number,\n    pageNumber: number = 1,\n    pageSize: number = 10\n  ): Promise<PaginatedResponse<Product>> {\n    const filters: ProductFilterRequest = {\n      minPrice,\n      maxPrice,\n      pageNumber,\n      pageSize,\n      sortBy: 'price',\n      sortDirection: 'asc'\n    };\n    \n    return this.getFiltered(filters);\n  }\n\n  /**\n   * Get products created within date range\n   */\n  async getByDateRange(startDate: Date, endDate: Date): Promise<Product[]> {\n    const filters: ProductFilterRequest = {\n      createdAfter: ServiceUtils.formatDate(startDate),\n      createdBefore: ServiceUtils.formatDate(endDate),\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get product recommendations based on collection\n   */\n  async getRecommendations(productId: number, count: number = 5): Promise<Product[]> {\n    try {\n      const product = await this.getById(productId);\n      const relatedProducts = await this.getByCollection(product.collectionId);\n      \n      // Filter out the current product and return limited results\n      return relatedProducts\n        .filter(p => p.id !== productId)\n        .slice(0, count);\n    } catch (error) {\n      console.error('Error getting product recommendations:', error);\n      return [];\n    }\n  }\n}\n\n// Export singleton instance\nexport const productGetService = new ProductGetService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQO,MAAM,0BAA0B,wIAAA,CAAA,cAAW;IAChD;;GAEC,GACD,MAAM,SAA6B;QACjC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI;QACjD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI;IAEzD;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAoB;QAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;QAClD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;IAExD;IAEA;;GAEC,GACD,MAAM,gBAAgB,YAAoB,EAAsB;QAC9D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;QAC1D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;IAElE;IAEA;;GAEC,GACD,MAAM,aAAiC;QACrC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,OAAO;QACpD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,OAAO;IAE5D;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgB,EAAE,EAA6B;QAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAAE;QAAM;QAC/D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAmB,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAAE;QAAM;IAE9E;IAEA;;GAEC,GACD,MAAM,UAAU,QAAgB,EAAE,EAA6B;QAC7D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;YAAE;QAAM;QAC7D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAmB,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;YAAE;QAAM;IAE5E;IAEA;;GAEC,GACD,MAAM,OAAO,IAAY,EAAsB;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;YAAE;QAAK;QAC5D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;YAAE;QAAK;IAEpE;IAEA;;GAEC,GACD,MAAM,gBAAgB,QAAgB,EAAE,QAAgB,EAAsB;QAC5E,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,UAAU,EAAE;YAAE;YAAU;QAAS;QAC9E,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,UAAU,EAAE;YAAE;YAAU;QAAS;IAEtF;IAEA;;GAEC,GACD,MAAM,YAAY,OAA6B,EAAuC;QACpF,MAAM,eAAe,wIAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;QAErD,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAC5B;IAGN;IAEA;;GAEC,GACD,MAAM,aACJ,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACrB,SAAiB,WAAW,EAC5B,gBAAgC,MAAM,EACD;QACrC,MAAM,UAAgC;YACpC;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,SAAS,GAAW,EAAsB;QAC9C,MAAM,UAAgC;YACpC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,YAAY,YAAoB,EAAE,EAAsB;QAC5D,MAAM,UAAgC;YACpC,UAAU;YACV,UAAU;YACV,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,gBAAoC;QACxC,MAAM,UAAgC;YACpC,SAAS;YACT,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,yBACJ,QAAgB,EAChB,QAAgB,EAChB,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACgB;QACrC,MAAM,UAAgC;YACpC;YACA;YACA;YACA;YACA,QAAQ;YACR,eAAe;QACjB;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAAsB;QACvE,MAAM,UAAgC;YACpC,cAAc,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACtC,eAAe,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACvC,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,mBAAmB,SAAiB,EAAE,QAAgB,CAAC,EAAsB;QACjF,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC;YACnC,MAAM,kBAAkB,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,YAAY;YAEvE,4DAA4D;YAC5D,OAAO,gBACJ,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WACrB,KAAK,CAAC,GAAG;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO,EAAE;QACX;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/post.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { Product, CreateProductRequest } from '../../types/entities';\n\nexport class ProductPostService extends BaseService {\n  /**\n   * Create a new product\n   */\n  async create(data: CreateProductRequest): Promise<Product> {\n    this.logApiCall('POST', ApiEndpoints.Products.Base, data);\n    \n    // Validate required fields\n    this.validateCreateRequest(data);\n    \n    return this.handleResponse(\n      this.client.post<Product>(ApiEndpoints.Products.Base, data)\n    );\n  }\n\n  /**\n   * Create a simple product with minimal data\n   */\n  async createSimple(\n    name: string,\n    description: string,\n    price: number,\n    stock: number,\n    collectionId: number,\n    images: string[] = [],\n    tags: string[] = []\n  ): Promise<Product> {\n    const data: CreateProductRequest = {\n      name,\n      description,\n      price,\n      stock,\n      collectionId,\n      images,\n      tags\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Create multiple products in batch\n   */\n  async createBatch(products: CreateProductRequest[]): Promise<Product[]> {\n    this.logApiCall('POST', 'Batch Products', { count: products.length });\n    \n    const promises = products.map(product => this.create(product));\n    return Promise.all(promises);\n  }\n\n  /**\n   * Create product with image upload\n   */\n  async createWithImages(\n    productData: Omit<CreateProductRequest, 'images'>,\n    imageFiles: File[]\n  ): Promise<Product> {\n    try {\n      // First upload images (this would need an image upload service)\n      const imageUrls = await this.uploadImages(imageFiles);\n      \n      // Then create product with image URLs\n      const data: CreateProductRequest = {\n        ...productData,\n        images: imageUrls\n      };\n\n      return this.create(data);\n    } catch (error) {\n      console.error('Error creating product with images:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Duplicate an existing product\n   */\n  async duplicate(\n    originalProductId: number,\n    newName: string,\n    modifications: Partial<CreateProductRequest> = {}\n  ): Promise<Product> {\n    try {\n      // Get the original product\n      const originalResponse = await this.client.get<Product>(\n        ApiEndpoints.Products.ById(originalProductId)\n      );\n\n      if (!originalResponse.success || !originalResponse.data) {\n        throw new Error('Original product not found');\n      }\n\n      const original = originalResponse.data;\n\n      // Create new product data based on original\n      const data: CreateProductRequest = {\n        name: newName,\n        description: original.description,\n        price: original.price,\n        stock: 0, // Start with 0 stock for duplicated products\n        collectionId: original.collectionId,\n        images: [...original.images], // Copy images array\n        tags: [...original.tags], // Copy tags array\n        ...modifications // Apply any modifications\n      };\n\n      return this.create(data);\n    } catch (error) {\n      console.error('Error duplicating product:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create product variant (similar product with different attributes)\n   */\n  async createVariant(\n    baseProductId: number,\n    variantName: string,\n    priceAdjustment: number = 0,\n    modifications: Partial<CreateProductRequest> = {}\n  ): Promise<Product> {\n    try {\n      const baseResponse = await this.client.get<Product>(\n        ApiEndpoints.Products.ById(baseProductId)\n      );\n\n      if (!baseResponse.success || !baseResponse.data) {\n        throw new Error('Base product not found');\n      }\n\n      const base = baseResponse.data;\n\n      const data: CreateProductRequest = {\n        name: `${base.name} - ${variantName}`,\n        description: base.description,\n        price: base.price + priceAdjustment,\n        stock: 0,\n        collectionId: base.collectionId,\n        images: [...base.images],\n        tags: [...base.tags, 'variant'],\n        ...modifications\n      };\n\n      return this.create(data);\n    } catch (error) {\n      console.error('Error creating product variant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Upload product images (placeholder - would need actual image upload service)\n   */\n  private async uploadImages(files: File[]): Promise<string[]> {\n    // This is a placeholder implementation\n    // In a real application, you would upload to a cloud storage service\n    const imageUrls: string[] = [];\n    \n    for (const file of files) {\n      // Simulate upload process\n      const formData = new FormData();\n      formData.append('image', file);\n      \n      try {\n        // This would be your actual image upload endpoint\n        // const response = await this.client.upload('/upload/image', formData);\n        // imageUrls.push(response.data.url);\n        \n        // For now, just create a placeholder URL\n        imageUrls.push(`/images/products/${Date.now()}-${file.name}`);\n      } catch (error) {\n        console.error('Error uploading image:', error);\n        throw new Error(`Failed to upload image: ${file.name}`);\n      }\n    }\n    \n    return imageUrls;\n  }\n\n  /**\n   * Validate create product request\n   */\n  private validateCreateRequest(data: CreateProductRequest): void {\n    if (!data.name || data.name.trim().length === 0) {\n      throw new Error('Product name is required');\n    }\n\n    if (data.name.length > 200) {\n      throw new Error('Product name must be 200 characters or less');\n    }\n\n    if (data.price <= 0) {\n      throw new Error('Product price must be greater than 0');\n    }\n\n    if (data.stock < 0) {\n      throw new Error('Product stock cannot be negative');\n    }\n\n    if (!data.collectionId) {\n      throw new Error('Collection ID is required');\n    }\n\n    if (data.description && data.description.length > 1000) {\n      throw new Error('Description must be 1000 characters or less');\n    }\n\n    // Validate image URLs\n    if (data.images && data.images.length > 0) {\n      for (const image of data.images) {\n        if (!this.isValidImageUrl(image)) {\n          throw new Error(`Invalid image URL: ${image}`);\n        }\n      }\n    }\n  }\n\n  /**\n   * Validate image URL format\n   */\n  private isValidImageUrl(url: string): boolean {\n    try {\n      new URL(url);\n      return true;\n    } catch {\n      // If not a valid URL, check if it's a relative path\n      return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');\n    }\n  }\n}\n\n// Export singleton instance\nexport const productPostService = new ProductPostService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,2BAA2B,wIAAA,CAAA,cAAW;IACjD;;GAEC,GACD,MAAM,OAAO,IAA0B,EAAoB;QACzD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,EAAE;QAEpD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,EAAE;IAE1D;IAEA;;GAEC,GACD,MAAM,aACJ,IAAY,EACZ,WAAmB,EACnB,KAAa,EACb,KAAa,EACb,YAAoB,EACpB,SAAmB,EAAE,EACrB,OAAiB,EAAE,EACD;QAClB,MAAM,OAA6B;YACjC;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgC,EAAsB;QACtE,IAAI,CAAC,UAAU,CAAC,QAAQ,kBAAkB;YAAE,OAAO,SAAS,MAAM;QAAC;QAEnE,MAAM,WAAW,SAAS,GAAG,CAAC,CAAA,UAAW,IAAI,CAAC,MAAM,CAAC;QACrD,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,iBACJ,WAAiD,EACjD,UAAkB,EACA;QAClB,IAAI;YACF,gEAAgE;YAChE,MAAM,YAAY,MAAM,IAAI,CAAC,YAAY,CAAC;YAE1C,sCAAsC;YACtC,MAAM,OAA6B;gBACjC,GAAG,WAAW;gBACd,QAAQ;YACV;YAEA,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,UACJ,iBAAyB,EACzB,OAAe,EACf,gBAA+C,CAAC,CAAC,EAC/B;QAClB,IAAI;YACF,2BAA2B;YAC3B,MAAM,mBAAmB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC5C,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YAG7B,IAAI,CAAC,iBAAiB,OAAO,IAAI,CAAC,iBAAiB,IAAI,EAAE;gBACvD,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,iBAAiB,IAAI;YAEtC,4CAA4C;YAC5C,MAAM,OAA6B;gBACjC,MAAM;gBACN,aAAa,SAAS,WAAW;gBACjC,OAAO,SAAS,KAAK;gBACrB,OAAO;gBACP,cAAc,SAAS,YAAY;gBACnC,QAAQ;uBAAI,SAAS,MAAM;iBAAC;gBAC5B,MAAM;uBAAI,SAAS,IAAI;iBAAC;gBACxB,GAAG,cAAc,0BAA0B;YAC7C;YAEA,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cACJ,aAAqB,EACrB,WAAmB,EACnB,kBAA0B,CAAC,EAC3B,gBAA+C,CAAC,CAAC,EAC/B;QAClB,IAAI;YACF,MAAM,eAAe,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CACxC,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YAG7B,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,aAAa,IAAI,EAAE;gBAC/C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,aAAa,IAAI;YAE9B,MAAM,OAA6B;gBACjC,MAAM,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,aAAa;gBACrC,aAAa,KAAK,WAAW;gBAC7B,OAAO,KAAK,KAAK,GAAG;gBACpB,OAAO;gBACP,cAAc,KAAK,YAAY;gBAC/B,QAAQ;uBAAI,KAAK,MAAM;iBAAC;gBACxB,MAAM;uBAAI,KAAK,IAAI;oBAAE;iBAAU;gBAC/B,GAAG,aAAa;YAClB;YAEA,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAc,aAAa,KAAa,EAAqB;QAC3D,uCAAuC;QACvC,qEAAqE;QACrE,MAAM,YAAsB,EAAE;QAE9B,KAAK,MAAM,QAAQ,MAAO;YACxB,0BAA0B;YAC1B,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YAEzB,IAAI;gBACF,kDAAkD;gBAClD,wEAAwE;gBACxE,qCAAqC;gBAErC,yCAAyC;gBACzC,UAAU,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;YAC9D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YACxD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAA0B,EAAQ;QAC9D,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,IAAI,GAAG;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,GAAG,GAAG;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,sBAAsB;QACtB,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;YACzC,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ;oBAChC,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,OAAO;gBAC/C;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,GAAW,EAAW;QAC5C,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,oDAAoD;YACpD,OAAO,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC;QACvE;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { Product, UpdateProductRequest } from '../../types/entities';\n\nexport class ProductUpdateService extends BaseService {\n  /**\n   * Update an existing product\n   */\n  async update(id: number, data: UpdateProductRequest): Promise<Product> {\n    this.logApiCall('PUT', ApiEndpoints.Products.ById(id), data);\n    \n    // Validate required fields\n    this.validateUpdateRequest(data);\n    \n    return this.handleResponse(\n      this.client.put<Product>(ApiEndpoints.Products.ById(id), data)\n    );\n  }\n\n  /**\n   * Update product stock\n   */\n  async updateStock(id: number, newStock: number): Promise<boolean> {\n    this.logApiCall('PATCH', ApiEndpoints.Products.UpdateStock(id), { stock: newStock });\n    \n    if (newStock < 0) {\n      throw new Error('Stock cannot be negative');\n    }\n    \n    return this.handleVoidResponse(\n      this.client.patch(ApiEndpoints.Products.UpdateStock(id), newStock)\n    );\n  }\n\n  /**\n   * Update product price\n   */\n  async updatePrice(id: number, newPrice: number): Promise<Product> {\n    if (newPrice <= 0) {\n      throw new Error('Price must be greater than 0');\n    }\n\n    const currentProduct = await this.getCurrentProduct(id);\n    \n    const data: UpdateProductRequest = {\n      ...this.mapProductToUpdateRequest(currentProduct),\n      price: newPrice\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Update product basic information\n   */\n  async updateBasicInfo(\n    id: number,\n    name: string,\n    description: string\n  ): Promise<Product> {\n    const currentProduct = await this.getCurrentProduct(id);\n    \n    const data: UpdateProductRequest = {\n      ...this.mapProductToUpdateRequest(currentProduct),\n      name,\n      description\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Update product images\n   */\n  async updateImages(id: number, images: string[]): Promise<Product> {\n    const currentProduct = await this.getCurrentProduct(id);\n    \n    const data: UpdateProductRequest = {\n      ...this.mapProductToUpdateRequest(currentProduct),\n      images\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Add images to product\n   */\n  async addImages(id: number, newImages: string[]): Promise<Product> {\n    const currentProduct = await this.getCurrentProduct(id);\n    const existingImages = currentProduct.images || [];\n    const allImages = [...existingImages, ...newImages];\n\n    return this.updateImages(id, allImages);\n  }\n\n  /**\n   * Remove images from product\n   */\n  async removeImages(id: number, imagesToRemove: string[]): Promise<Product> {\n    const currentProduct = await this.getCurrentProduct(id);\n    const existingImages = currentProduct.images || [];\n    const filteredImages = existingImages.filter(img => !imagesToRemove.includes(img));\n\n    return this.updateImages(id, filteredImages);\n  }\n\n  /**\n   * Update product tags\n   */\n  async updateTags(id: number, tags: string[]): Promise<Product> {\n    const currentProduct = await this.getCurrentProduct(id);\n    \n    const data: UpdateProductRequest = {\n      ...this.mapProductToUpdateRequest(currentProduct),\n      tags\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Add tags to product\n   */\n  async addTags(id: number, newTags: string[]): Promise<Product> {\n    const currentProduct = await this.getCurrentProduct(id);\n    const existingTags = currentProduct.tags || [];\n    const uniqueTags = [...new Set([...existingTags, ...newTags])];\n\n    return this.updateTags(id, uniqueTags);\n  }\n\n  /**\n   * Remove tags from product\n   */\n  async removeTags(id: number, tagsToRemove: string[]): Promise<Product> {\n    const currentProduct = await this.getCurrentProduct(id);\n    const existingTags = currentProduct.tags || [];\n    const filteredTags = existingTags.filter(tag => !tagsToRemove.includes(tag));\n\n    return this.updateTags(id, filteredTags);\n  }\n\n  /**\n   * Move product to different collection\n   */\n  async moveToCollection(id: number, newCollectionId: number): Promise<Product> {\n    const currentProduct = await this.getCurrentProduct(id);\n    \n    const data: UpdateProductRequest = {\n      ...this.mapProductToUpdateRequest(currentProduct),\n      collectionId: newCollectionId\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Adjust stock (add or subtract)\n   */\n  async adjustStock(id: number, adjustment: number): Promise<boolean> {\n    const currentProduct = await this.getCurrentProduct(id);\n    const newStock = currentProduct.stock + adjustment;\n    \n    if (newStock < 0) {\n      throw new Error('Stock adjustment would result in negative stock');\n    }\n\n    return this.updateStock(id, newStock);\n  }\n\n  /**\n   * Increase stock\n   */\n  async increaseStock(id: number, amount: number): Promise<boolean> {\n    if (amount <= 0) {\n      throw new Error('Amount must be positive');\n    }\n    return this.adjustStock(id, amount);\n  }\n\n  /**\n   * Decrease stock\n   */\n  async decreaseStock(id: number, amount: number): Promise<boolean> {\n    if (amount <= 0) {\n      throw new Error('Amount must be positive');\n    }\n    return this.adjustStock(id, -amount);\n  }\n\n  /**\n   * Apply discount to product price\n   */\n  async applyDiscount(\n    id: number, \n    discountPercentage: number\n  ): Promise<Product> {\n    if (discountPercentage < 0 || discountPercentage > 100) {\n      throw new Error('Discount percentage must be between 0 and 100');\n    }\n\n    const currentProduct = await this.getCurrentProduct(id);\n    const discountedPrice = currentProduct.price * (1 - discountPercentage / 100);\n    \n    return this.updatePrice(id, Math.round(discountedPrice * 100) / 100);\n  }\n\n  /**\n   * Get current product data\n   */\n  private async getCurrentProduct(id: number): Promise<Product> {\n    const response = await this.client.get<Product>(ApiEndpoints.Products.ById(id));\n    \n    if (!response.success || !response.data) {\n      throw new Error('Product not found');\n    }\n    \n    return response.data;\n  }\n\n  /**\n   * Map Product to UpdateProductRequest\n   */\n  private mapProductToUpdateRequest(product: Product): UpdateProductRequest {\n    return {\n      name: product.name,\n      description: product.description,\n      price: product.price,\n      stock: product.stock,\n      collectionId: product.collectionId,\n      images: product.images,\n      tags: product.tags\n    };\n  }\n\n  /**\n   * Validate update product request\n   */\n  private validateUpdateRequest(data: UpdateProductRequest): void {\n    if (!data.name || data.name.trim().length === 0) {\n      throw new Error('Product name is required');\n    }\n\n    if (data.name.length > 200) {\n      throw new Error('Product name must be 200 characters or less');\n    }\n\n    if (data.price <= 0) {\n      throw new Error('Product price must be greater than 0');\n    }\n\n    if (data.stock < 0) {\n      throw new Error('Product stock cannot be negative');\n    }\n\n    if (!data.collectionId) {\n      throw new Error('Collection ID is required');\n    }\n\n    if (data.description && data.description.length > 1000) {\n      throw new Error('Description must be 1000 characters or less');\n    }\n  }\n}\n\n// Export singleton instance\nexport const productUpdateService = new ProductUpdateService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,6BAA6B,wIAAA,CAAA,cAAW;IACnD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAA0B,EAAoB;QACrE,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK;QAEvD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK;IAE7D;IAEA;;GAEC,GACD,MAAM,YAAY,EAAU,EAAE,QAAgB,EAAoB;QAChE,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK;YAAE,OAAO;QAAS;QAElF,IAAI,WAAW,GAAG;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK;IAE7D;IAEA;;GAEC,GACD,MAAM,YAAY,EAAU,EAAE,QAAgB,EAAoB;QAChE,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD,OAAO;QACT;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,gBACJ,EAAU,EACV,IAAY,EACZ,WAAmB,EACD;QAClB,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,aAAa,EAAU,EAAE,MAAgB,EAAoB;QACjE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAAE,SAAmB,EAAoB;QACjE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,iBAAiB,eAAe,MAAM,IAAI,EAAE;QAClD,MAAM,YAAY;eAAI;eAAmB;SAAU;QAEnD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI;IAC/B;IAEA;;GAEC,GACD,MAAM,aAAa,EAAU,EAAE,cAAwB,EAAoB;QACzE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,iBAAiB,eAAe,MAAM,IAAI,EAAE;QAClD,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,MAAO,CAAC,eAAe,QAAQ,CAAC;QAE7E,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI;IAC/B;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAE,IAAc,EAAoB;QAC7D,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAE,OAAiB,EAAoB;QAC7D,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,eAAe,eAAe,IAAI,IAAI,EAAE;QAC9C,MAAM,aAAa;eAAI,IAAI,IAAI;mBAAI;mBAAiB;aAAQ;SAAE;QAE9D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAE,YAAsB,EAAoB;QACrE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,eAAe,eAAe,IAAI,IAAI,EAAE;QAC9C,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA,MAAO,CAAC,aAAa,QAAQ,CAAC;QAEvE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAE,eAAuB,EAAoB;QAC5E,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD,cAAc;QAChB;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,YAAY,EAAU,EAAE,UAAkB,EAAoB;QAClE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,WAAW,eAAe,KAAK,GAAG;QAExC,IAAI,WAAW,GAAG;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAC9B;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAE,MAAc,EAAoB;QAChE,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAC9B;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAE,MAAc,EAAoB;QAChE,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IAC/B;IAEA;;GAEC,GACD,MAAM,cACJ,EAAU,EACV,kBAA0B,EACR;QAClB,IAAI,qBAAqB,KAAK,qBAAqB,KAAK;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,kBAAkB,eAAe,KAAK,GAAG,CAAC,IAAI,qBAAqB,GAAG;QAE5E,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,KAAK,CAAC,kBAAkB,OAAO;IAClE;IAEA;;GAEC,GACD,MAAc,kBAAkB,EAAU,EAAoB;QAC5D,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;QAE3E,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,AAAQ,0BAA0B,OAAgB,EAAwB;QACxE,OAAO;YACL,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,KAAK;YACpB,cAAc,QAAQ,YAAY;YAClC,QAAQ,QAAQ,MAAM;YACtB,MAAM,QAAQ,IAAI;QACpB;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAA0B,EAAQ;QAC9D,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,IAAI,GAAG;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,GAAG,GAAG;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/delete.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\n\nexport class ProductDeleteService extends BaseService {\n  /**\n   * Delete a product by ID\n   */\n  async delete(id: number): Promise<boolean> {\n    this.logApiCall('DELETE', ApiEndpoints.Products.ById(id));\n    \n    return this.handleVoidResponse(\n      this.client.delete(ApiEndpoints.Products.ById(id))\n    );\n  }\n\n  /**\n   * Delete multiple products\n   */\n  async deleteBatch(ids: number[]): Promise<boolean[]> {\n    this.logApiCall('DELETE', 'Batch Products', { count: ids.length });\n    \n    const promises = ids.map(id => this.delete(id));\n    return Promise.all(promises);\n  }\n\n  /**\n   * Soft delete - set stock to 0 instead of deleting\n   */\n  async softDelete(id: number): Promise<boolean> {\n    this.logApiCall('PATCH', `Soft Delete Product ${id}`);\n    \n    try {\n      // Import here to avoid circular dependency\n      const { productUpdateService } = await import('./update');\n      await productUpdateService.updateStock(id, 0);\n      return true;\n    } catch (error) {\n      console.error('Failed to soft delete product:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Check if product can be safely deleted\n   */\n  async canDelete(id: number): Promise<{\n    canDelete: boolean;\n    reason?: string;\n    hasOrders?: boolean;\n    isInActiveOrders?: boolean;\n  }> {\n    try {\n      // This would require checking if product is in any orders\n      // For now, we'll assume it can be deleted\n      // In a real implementation, you'd check:\n      // 1. If product is in any pending orders\n      // 2. If product is in any active shopping carts\n      // 3. If product has any dependencies\n      \n      return {\n        canDelete: true,\n        hasOrders: false,\n        isInActiveOrders: false\n      };\n    } catch (error) {\n      console.error('Error checking if product can be deleted:', error);\n      return {\n        canDelete: false,\n        reason: 'Error checking product dependencies'\n      };\n    }\n  }\n\n  /**\n   * Safe delete - checks dependencies before deleting\n   */\n  async safeDelete(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const deleteCheck = await this.canDelete(id);\n      \n      if (!deleteCheck.canDelete) {\n        return {\n          success: false,\n          message: deleteCheck.reason || 'Product cannot be deleted'\n        };\n      }\n\n      const deleted = await this.delete(id);\n      \n      if (deleted) {\n        return {\n          success: true,\n          message: 'Product deleted successfully'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Failed to delete product'\n        };\n      }\n    } catch (error) {\n      console.error('Error during safe delete:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\n      };\n    }\n  }\n\n  /**\n   * Archive product (soft delete with archive flag)\n   */\n  async archive(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      // Set stock to 0 and add archive tag\n      const { productUpdateService } = await import('./update');\n      \n      await productUpdateService.updateStock(id, 0);\n      await productUpdateService.addTags(id, ['archived']);\n      \n      return {\n        success: true,\n        message: 'Product archived successfully'\n      };\n    } catch (error) {\n      console.error('Error archiving product:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to archive product'\n      };\n    }\n  }\n\n  /**\n   * Restore archived product\n   */\n  async restore(id: number, newStock: number = 1): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const { productUpdateService } = await import('./update');\n      \n      await productUpdateService.updateStock(id, newStock);\n      await productUpdateService.removeTags(id, ['archived']);\n      \n      return {\n        success: true,\n        message: 'Product restored successfully'\n      };\n    } catch (error) {\n      console.error('Error restoring product:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to restore product'\n      };\n    }\n  }\n\n  /**\n   * Delete all products in a collection\n   */\n  async deleteByCollection(collectionId: number): Promise<{\n    success: boolean;\n    message: string;\n    deletedCount: number;\n  }> {\n    try {\n      // Get all products in the collection\n      const { productGetService } = await import('./get');\n      const products = await productGetService.getByCollection(collectionId);\n      \n      if (products.length === 0) {\n        return {\n          success: true,\n          message: 'No products found in collection',\n          deletedCount: 0\n        };\n      }\n\n      // Delete all products\n      const productIds = products.map(p => p.id);\n      const results = await this.deleteBatch(productIds);\n      const deletedCount = results.filter(result => result).length;\n      \n      return {\n        success: deletedCount === products.length,\n        message: `Deleted ${deletedCount} of ${products.length} products`,\n        deletedCount\n      };\n    } catch (error) {\n      console.error('Error deleting products by collection:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to delete products',\n        deletedCount: 0\n      };\n    }\n  }\n\n  /**\n   * Delete out of stock products\n   */\n  async deleteOutOfStock(): Promise<{\n    success: boolean;\n    message: string;\n    deletedCount: number;\n  }> {\n    try {\n      const { productGetService } = await import('./get');\n      const outOfStockProducts = await productGetService.getOutOfStock();\n      \n      if (outOfStockProducts.length === 0) {\n        return {\n          success: true,\n          message: 'No out of stock products found',\n          deletedCount: 0\n        };\n      }\n\n      const productIds = outOfStockProducts.map(p => p.id);\n      const results = await this.deleteBatch(productIds);\n      const deletedCount = results.filter(result => result).length;\n      \n      return {\n        success: deletedCount === outOfStockProducts.length,\n        message: `Deleted ${deletedCount} out of stock products`,\n        deletedCount\n      };\n    } catch (error) {\n      console.error('Error deleting out of stock products:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to delete out of stock products',\n        deletedCount: 0\n      };\n    }\n  }\n}\n\n// Export singleton instance\nexport const productDeleteService = new ProductDeleteService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,6BAA6B,wIAAA,CAAA,cAAW;IACnD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;QAErD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;IAElD;IAEA;;GAEC,GACD,MAAM,YAAY,GAAa,EAAsB;QACnD,IAAI,CAAC,UAAU,CAAC,UAAU,kBAAkB;YAAE,OAAO,IAAI,MAAM;QAAC;QAEhE,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,MAAM,CAAC;QAC3C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAoB;QAC7C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI;QAEpD,IAAI;YACF,2CAA2C;YAC3C,MAAM,EAAE,oBAAoB,EAAE,GAAG;YACjC,MAAM,qBAAqB,WAAW,CAAC,IAAI;YAC3C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAKvB;QACD,IAAI;YACF,0DAA0D;YAC1D,0CAA0C;YAC1C,yCAAyC;YACzC,yCAAyC;YACzC,gDAAgD;YAChD,qCAAqC;YAErC,OAAO;gBACL,WAAW;gBACX,WAAW;gBACX,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAGxB;QACD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC;YAEzC,IAAI,CAAC,YAAY,SAAS,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,YAAY,MAAM,IAAI;gBACjC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAElC,IAAI,SAAS;gBACX,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAGrB;QACD,IAAI;YACF,qCAAqC;YACrC,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,MAAM,qBAAqB,WAAW,CAAC,IAAI;YAC3C,MAAM,qBAAqB,OAAO,CAAC,IAAI;gBAAC;aAAW;YAEnD,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAE,WAAmB,CAAC,EAG3C;QACD,IAAI;YACF,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,MAAM,qBAAqB,WAAW,CAAC,IAAI;YAC3C,MAAM,qBAAqB,UAAU,CAAC,IAAI;gBAAC;aAAW;YAEtD,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,YAAoB,EAI1C;QACD,IAAI;YACF,qCAAqC;YACrC,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,WAAW,MAAM,kBAAkB,eAAe,CAAC;YAEzD,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,sBAAsB;YACtB,MAAM,aAAa,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACzC,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,SAAS,MAAM;gBACzC,SAAS,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;gBACjE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBAIH;QACD,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,qBAAqB,MAAM,kBAAkB,aAAa;YAEhE,IAAI,mBAAmB,MAAM,KAAK,GAAG;gBACnC,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,MAAM,aAAa,mBAAmB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACnD,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,mBAAmB,MAAM;gBACnD,SAAS,CAAC,QAAQ,EAAE,aAAa,sBAAsB,CAAC;gBACxD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/index.ts"], "sourcesContent": ["// Product Services\nexport { ProductGetService, productGetService } from './get';\nexport { ProductPostService, productPostService } from './post';\nexport { ProductUpdateService, productUpdateService } from './update';\nexport { ProductDeleteService, productDeleteService } from './delete';\n\n// Combined Product Service\nimport { productGetService } from './get';\nimport { productPostService } from './post';\nimport { productUpdateService } from './update';\nimport { productDeleteService } from './delete';\n\nexport class ProductService {\n  get = productGetService;\n  post = productPostService;\n  update = productUpdateService;\n  delete = productDeleteService;\n}\n\n// Export singleton instance\nexport const productService = new ProductService();\n"], "names": [], "mappings": "AAAA,mBAAmB;;;;;AACnB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,yIAAA,CAAA,oBAAiB,CAAC;IACxB,OAAO,0IAAA,CAAA,qBAAkB,CAAC;IAC1B,SAAS,4IAAA,CAAA,uBAAoB,CAAC;IAC9B,SAAS,4IAAA,CAAA,uBAAoB,CAAC;AAChC;AAGO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 1219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/get.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { \n  Order, \n  OrderSummary, \n  OrderFilterRequest,\n  PaginatedResponse \n} from '../../types/entities';\n\nexport class OrderGetService extends BaseService {\n  /**\n   * Get all orders (summary)\n   */\n  async getAll(): Promise<OrderSummary[]> {\n    this.logApiCall('GET', ApiEndpoints.Orders.Base);\n    return this.handleResponse(\n      this.client.get<OrderSummary[]>(ApiEndpoints.Orders.Base)\n    );\n  }\n\n  /**\n   * Get order by ID\n   */\n  async getById(id: number): Promise<Order> {\n    this.logApiCall('GET', ApiEndpoints.Orders.ById(id));\n    return this.handleResponse(\n      this.client.get<Order>(ApiEndpoints.Orders.ById(id))\n    );\n  }\n\n  /**\n   * Get order with full details\n   */\n  async getDetails(id: number): Promise<Order> {\n    this.logApiCall('GET', ApiEndpoints.Orders.Details(id));\n    return this.handleResponse(\n      this.client.get<Order>(ApiEndpoints.Orders.Details(id))\n    );\n  }\n\n  /**\n   * Get orders by user ID\n   */\n  async getByUser(userId: number): Promise<Order[]> {\n    this.logApiCall('GET', ApiEndpoints.Orders.ByUser(userId));\n    return this.handleResponse(\n      this.client.get<Order[]>(ApiEndpoints.Orders.ByUser(userId))\n    );\n  }\n\n  /**\n   * Get orders by email\n   */\n  async getByEmail(email: string): Promise<Order[]> {\n    this.logApiCall('GET', ApiEndpoints.Orders.ByEmail(email));\n    return this.handleResponse(\n      this.client.get<Order[]>(ApiEndpoints.Orders.ByEmail(email))\n    );\n  }\n\n  /**\n   * Get orders by status\n   */\n  async getByStatus(statusId: number): Promise<Order[]> {\n    this.logApiCall('GET', ApiEndpoints.Orders.ByStatus(statusId));\n    return this.handleResponse(\n      this.client.get<Order[]>(ApiEndpoints.Orders.ByStatus(statusId))\n    );\n  }\n\n  /**\n   * Get pending orders\n   */\n  async getPending(): Promise<Order[]> {\n    this.logApiCall('GET', ApiEndpoints.Orders.Pending);\n    return this.handleResponse(\n      this.client.get<Order[]>(ApiEndpoints.Orders.Pending)\n    );\n  }\n\n  /**\n   * Get recent orders\n   */\n  async getRecent(count: number = 10): Promise<OrderSummary[]> {\n    this.logApiCall('GET', ApiEndpoints.Orders.Recent, { count });\n    return this.handleResponse(\n      this.client.get<OrderSummary[]>(ApiEndpoints.Orders.Recent, { count })\n    );\n  }\n\n  /**\n   * Get total revenue\n   */\n  async getTotalRevenue(): Promise<number> {\n    this.logApiCall('GET', ApiEndpoints.Orders.Revenue.Total);\n    return this.handleResponse(\n      this.client.get<number>(ApiEndpoints.Orders.Revenue.Total)\n    );\n  }\n\n  /**\n   * Get revenue by date range\n   */\n  async getRevenueByDateRange(startDate: Date, endDate: Date): Promise<number> {\n    const params = {\n      startDate: ServiceUtils.formatDate(startDate),\n      endDate: ServiceUtils.formatDate(endDate)\n    };\n    \n    this.logApiCall('GET', ApiEndpoints.Orders.Revenue.Range, params);\n    return this.handleResponse(\n      this.client.get<number>(ApiEndpoints.Orders.Revenue.Range, params)\n    );\n  }\n\n  /**\n   * Get orders with advanced filtering and pagination\n   */\n  async getFiltered(filters: OrderFilterRequest): Promise<PaginatedResponse<Order>> {\n    const cleanFilters = ServiceUtils.cleanObject(filters);\n    this.logApiCall('GET', ApiEndpoints.Orders.Filter, cleanFilters);\n    \n    return this.handlePaginatedResponse(\n      this.client.get<PaginatedResponse<Order>>(\n        ApiEndpoints.Orders.Filter, \n        cleanFilters\n      )\n    );\n  }\n\n  /**\n   * Get orders with default pagination\n   */\n  async getPaginated(\n    pageNumber: number = 1, \n    pageSize: number = 10,\n    sortBy: string = 'createdAt',\n    sortDirection: 'asc' | 'desc' = 'desc'\n  ): Promise<PaginatedResponse<Order>> {\n    const filters: OrderFilterRequest = {\n      pageNumber,\n      pageSize,\n      sortBy,\n      sortDirection\n    };\n    \n    return this.getFiltered(filters);\n  }\n\n  /**\n   * Get orders by amount range\n   */\n  async getByAmountRange(minAmount: number, maxAmount: number): Promise<Order[]> {\n    const filters: OrderFilterRequest = {\n      minAmount,\n      maxAmount,\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get orders by payment method\n   */\n  async getByPaymentMethod(paymentMethod: string): Promise<Order[]> {\n    const filters: OrderFilterRequest = {\n      paymentMethod,\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get orders by country\n   */\n  async getByCountry(country: string): Promise<Order[]> {\n    const filters: OrderFilterRequest = {\n      country,\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get orders by city\n   */\n  async getByCity(city: string): Promise<Order[]> {\n    const filters: OrderFilterRequest = {\n      city,\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get orders created within date range\n   */\n  async getByDateRange(startDate: Date, endDate: Date): Promise<Order[]> {\n    const filters: OrderFilterRequest = {\n      createdAfter: ServiceUtils.formatDate(startDate),\n      createdBefore: ServiceUtils.formatDate(endDate),\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get orders by multiple statuses\n   */\n  async getByStatuses(statusIds: number[]): Promise<Order[]> {\n    const promises = statusIds.map(statusId => this.getByStatus(statusId));\n    const results = await Promise.all(promises);\n    \n    // Flatten and remove duplicates\n    const allOrders = results.flat();\n    const uniqueOrders = allOrders.filter((order, index, self) => \n      index === self.findIndex(o => o.id === order.id)\n    );\n    \n    return uniqueOrders;\n  }\n\n  /**\n   * Get user's order history with pagination\n   */\n  async getUserOrderHistory(\n    userId: number,\n    pageNumber: number = 1,\n    pageSize: number = 10\n  ): Promise<PaginatedResponse<Order>> {\n    const filters: OrderFilterRequest = {\n      userId,\n      pageNumber,\n      pageSize,\n      sortBy: 'createdAt',\n      sortDirection: 'desc'\n    };\n    \n    return this.getFiltered(filters);\n  }\n\n  /**\n   * Get guest orders by email with pagination\n   */\n  async getGuestOrderHistory(\n    email: string,\n    pageNumber: number = 1,\n    pageSize: number = 10\n  ): Promise<PaginatedResponse<Order>> {\n    const filters: OrderFilterRequest = {\n      email,\n      pageNumber,\n      pageSize,\n      sortBy: 'createdAt',\n      sortDirection: 'desc'\n    };\n    \n    return this.getFiltered(filters);\n  }\n\n  /**\n   * Get order statistics\n   */\n  async getStatistics(): Promise<{\n    totalOrders: number;\n    totalRevenue: number;\n    averageOrderValue: number;\n    pendingOrders: number;\n    completedOrders: number;\n  }> {\n    try {\n      const [totalRevenue, pendingOrders, allOrders] = await Promise.all([\n        this.getTotalRevenue(),\n        this.getPending(),\n        this.getAll()\n      ]);\n\n      const totalOrders = allOrders.length;\n      const completedOrders = allOrders.filter(order => \n        order.statusName.toLowerCase().includes('delivered') || \n        order.statusName.toLowerCase().includes('completed')\n      ).length;\n\n      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;\n\n      return {\n        totalOrders,\n        totalRevenue,\n        averageOrderValue: Math.round(averageOrderValue * 100) / 100,\n        pendingOrders: pendingOrders.length,\n        completedOrders\n      };\n    } catch (error) {\n      console.error('Error getting order statistics:', error);\n      throw error;\n    }\n  }\n}\n\n// Export singleton instance\nexport const orderGetService = new OrderGetService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQO,MAAM,wBAAwB,wIAAA,CAAA,cAAW;IAC9C;;GAEC,GACD,MAAM,SAAkC;QACtC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI;QAC/C,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAiB,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI;IAE5D;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAkB;QACxC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,CAAC;QAChD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAQ,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,CAAC;IAEpD;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAkB;QAC3C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC;QACnD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAQ,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC;IAEvD;IAEA;;GAEC,GACD,MAAM,UAAU,MAAc,EAAoB;QAChD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAC;QAClD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAC;IAExD;IAEA;;GAEC,GACD,MAAM,WAAW,KAAa,EAAoB;QAChD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC;QACnD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC;IAEzD;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgB,EAAoB;QACpD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;IAE1D;IAEA;;GAEC,GACD,MAAM,aAA+B;QACnC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO;QAClD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO;IAExD;IAEA;;GAEC,GACD,MAAM,UAAU,QAAgB,EAAE,EAA2B;QAC3D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,EAAE;YAAE;QAAM;QAC3D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAiB,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,EAAE;YAAE;QAAM;IAExE;IAEA;;GAEC,GACD,MAAM,kBAAmC;QACvC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK;QACxD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAS,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK;IAE7D;IAEA;;GAEC,GACD,MAAM,sBAAsB,SAAe,EAAE,OAAa,EAAmB;QAC3E,MAAM,SAAS;YACb,WAAW,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACnC,SAAS,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;QACnC;QAEA,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;QAC1D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAS,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;IAE/D;IAEA;;GAEC,GACD,MAAM,YAAY,OAA2B,EAAqC;QAChF,MAAM,eAAe,wIAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,EAAE;QAEnD,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,EAC1B;IAGN;IAEA;;GAEC,GACD,MAAM,aACJ,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACrB,SAAiB,WAAW,EAC5B,gBAAgC,MAAM,EACH;QACnC,MAAM,UAA8B;YAClC;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,iBAAiB,SAAiB,EAAE,SAAiB,EAAoB;QAC7E,MAAM,UAA8B;YAClC;YACA;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAoB;QAChE,MAAM,UAA8B;YAClC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,aAAa,OAAe,EAAoB;QACpD,MAAM,UAA8B;YAClC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,UAAU,IAAY,EAAoB;QAC9C,MAAM,UAA8B;YAClC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAAoB;QACrE,MAAM,UAA8B;YAClC,cAAc,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACtC,eAAe,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACvC,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,cAAc,SAAmB,EAAoB;QACzD,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,WAAY,IAAI,CAAC,WAAW,CAAC;QAC5D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAElC,gCAAgC;QAChC,MAAM,YAAY,QAAQ,IAAI;QAC9B,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,OAAO,OAAO,OACnD,UAAU,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;QAGjD,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACc;QACnC,MAAM,UAA8B;YAClC;YACA;YACA;YACA,QAAQ;YACR,eAAe;QACjB;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,qBACJ,KAAa,EACb,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACc;QACnC,MAAM,UAA8B;YAClC;YACA;YACA;YACA,QAAQ;YACR,eAAe;QACjB;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,gBAMH;QACD,IAAI;YACF,MAAM,CAAC,cAAc,eAAe,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjE,IAAI,CAAC,eAAe;gBACpB,IAAI,CAAC,UAAU;gBACf,IAAI,CAAC,MAAM;aACZ;YAED,MAAM,cAAc,UAAU,MAAM;YACpC,MAAM,kBAAkB,UAAU,MAAM,CAAC,CAAA,QACvC,MAAM,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACxC,MAAM,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,cACxC,MAAM;YAER,MAAM,oBAAoB,cAAc,IAAI,eAAe,cAAc;YAEzE,OAAO;gBACL;gBACA;gBACA,mBAAmB,KAAK,KAAK,CAAC,oBAAoB,OAAO;gBACzD,eAAe,cAAc,MAAM;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;AACF;AAGO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/post.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { Order, CreateOrderRequest, CreateOrderItemRequest } from '../../types/entities';\n\nexport class OrderPostService extends BaseService {\n  /**\n   * Create a new order\n   */\n  async create(data: CreateOrderRequest): Promise<Order> {\n    this.logApiCall('POST', ApiEndpoints.Orders.Base, data);\n    \n    // Validate required fields\n    this.validateCreateRequest(data);\n    \n    return this.handleResponse(\n      this.client.post<Order>(ApiEndpoints.Orders.Base, data)\n    );\n  }\n\n  /**\n   * Create a customer order (with user ID)\n   */\n  async createCustomerOrder(\n    userId: number,\n    email: string,\n    orderItems: CreateOrderItemRequest[],\n    shippingInfo: {\n      phoneNumber?: string;\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    } = {},\n    paymentMethod?: string\n  ): Promise<Order> {\n    const data: CreateOrderRequest = {\n      userId,\n      email,\n      phoneNumber: shippingInfo.phoneNumber,\n      country: shippingInfo.country,\n      city: shippingInfo.city,\n      zipCode: shippingInfo.zipCode,\n      paymentMethod,\n      orderItems\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Create a guest order (without user ID)\n   */\n  async createGuestOrder(\n    email: string,\n    orderItems: CreateOrderItemRequest[],\n    shippingInfo: {\n      phoneNumber?: string;\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    } = {},\n    paymentMethod?: string\n  ): Promise<Order> {\n    const data: CreateOrderRequest = {\n      userId: undefined,\n      email,\n      phoneNumber: shippingInfo.phoneNumber,\n      country: shippingInfo.country,\n      city: shippingInfo.city,\n      zipCode: shippingInfo.zipCode,\n      paymentMethod,\n      orderItems\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Create order from shopping cart\n   */\n  async createFromCart(\n    cartItems: Array<{\n      productId: number;\n      quantity: number;\n    }>,\n    customerInfo: {\n      userId?: number;\n      email: string;\n      phoneNumber?: string;\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    },\n    paymentMethod?: string\n  ): Promise<Order> {\n    const orderItems: CreateOrderItemRequest[] = cartItems.map(item => ({\n      productId: item.productId,\n      quantity: item.quantity\n    }));\n\n    const data: CreateOrderRequest = {\n      userId: customerInfo.userId,\n      email: customerInfo.email,\n      phoneNumber: customerInfo.phoneNumber,\n      country: customerInfo.country,\n      city: customerInfo.city,\n      zipCode: customerInfo.zipCode,\n      paymentMethod,\n      orderItems\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Create single item order (quick order)\n   */\n  async createQuickOrder(\n    productId: number,\n    quantity: number,\n    customerInfo: {\n      userId?: number;\n      email: string;\n      phoneNumber?: string;\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    },\n    paymentMethod?: string\n  ): Promise<Order> {\n    const orderItems: CreateOrderItemRequest[] = [{\n      productId,\n      quantity\n    }];\n\n    return this.createFromCart([{ productId, quantity }], customerInfo, paymentMethod);\n  }\n\n  /**\n   * Create bulk order (multiple products with quantities)\n   */\n  async createBulkOrder(\n    products: Array<{\n      productId: number;\n      quantity: number;\n    }>,\n    customerInfo: {\n      userId?: number;\n      email: string;\n      phoneNumber?: string;\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    },\n    paymentMethod?: string\n  ): Promise<Order> {\n    return this.createFromCart(products, customerInfo, paymentMethod);\n  }\n\n  /**\n   * Create repeat order (duplicate previous order)\n   */\n  async createRepeatOrder(\n    originalOrderId: number,\n    customerInfo: {\n      userId?: number;\n      email: string;\n      phoneNumber?: string;\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    },\n    paymentMethod?: string\n  ): Promise<Order> {\n    try {\n      // Get the original order details\n      const { orderGetService } = await import('./get');\n      const originalOrder = await orderGetService.getDetails(originalOrderId);\n\n      // Create new order items from original order\n      const orderItems: CreateOrderItemRequest[] = originalOrder.orderItems.map(item => ({\n        productId: item.productId,\n        quantity: item.quantity\n      }));\n\n      const data: CreateOrderRequest = {\n        userId: customerInfo.userId,\n        email: customerInfo.email,\n        phoneNumber: customerInfo.phoneNumber,\n        country: customerInfo.country,\n        city: customerInfo.city,\n        zipCode: customerInfo.zipCode,\n        paymentMethod,\n        orderItems\n      };\n\n      return this.create(data);\n    } catch (error) {\n      console.error('Error creating repeat order:', error);\n      throw new Error('Failed to create repeat order');\n    }\n  }\n\n  /**\n   * Create order with validation and stock check\n   */\n  async createWithValidation(data: CreateOrderRequest): Promise<{\n    success: boolean;\n    order?: Order;\n    errors?: string[];\n  }> {\n    try {\n      // Validate stock availability (this would require product service)\n      const stockValidation = await this.validateStock(data.orderItems);\n      \n      if (!stockValidation.valid) {\n        return {\n          success: false,\n          errors: stockValidation.errors\n        };\n      }\n\n      const order = await this.create(data);\n      \n      return {\n        success: true,\n        order\n      };\n    } catch (error) {\n      console.error('Error creating order with validation:', error);\n      return {\n        success: false,\n        errors: [error instanceof Error ? error.message : 'Unknown error occurred']\n      };\n    }\n  }\n\n  /**\n   * Validate stock availability for order items\n   */\n  private async validateStock(orderItems: CreateOrderItemRequest[]): Promise<{\n    valid: boolean;\n    errors?: string[];\n  }> {\n    try {\n      // This would require product service to check stock\n      // For now, we'll assume stock is valid\n      // In a real implementation, you'd check each product's stock\n      \n      const errors: string[] = [];\n      \n      for (const item of orderItems) {\n        if (item.quantity <= 0) {\n          errors.push(`Invalid quantity for product ${item.productId}`);\n        }\n      }\n\n      return {\n        valid: errors.length === 0,\n        errors: errors.length > 0 ? errors : undefined\n      };\n    } catch (error) {\n      console.error('Error validating stock:', error);\n      return {\n        valid: false,\n        errors: ['Error validating stock availability']\n      };\n    }\n  }\n\n  /**\n   * Validate create order request\n   */\n  private validateCreateRequest(data: CreateOrderRequest): void {\n    if (!data.email || !ServiceUtils.isValidEmail(data.email)) {\n      throw new Error('Valid email is required');\n    }\n\n    if (!data.orderItems || data.orderItems.length === 0) {\n      throw new Error('At least one order item is required');\n    }\n\n    // Validate order items\n    for (const item of data.orderItems) {\n      if (!item.productId || item.productId <= 0) {\n        throw new Error('Valid product ID is required for all order items');\n      }\n\n      if (!item.quantity || item.quantity <= 0) {\n        throw new Error('Quantity must be greater than 0 for all order items');\n      }\n    }\n\n    // Validate phone number format if provided\n    if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {\n      throw new Error('Invalid phone number format');\n    }\n\n    // Validate zip code format if provided\n    if (data.zipCode && data.zipCode.length > 20) {\n      throw new Error('Zip code must be 20 characters or less');\n    }\n  }\n\n  /**\n   * Validate phone number format\n   */\n  private isValidPhoneNumber(phone: string): boolean {\n    // Basic phone number validation (can be enhanced)\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n  }\n}\n\n// Export singleton instance\nexport const orderPostService = new OrderPostService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,yBAAyB,wIAAA,CAAA,cAAW;IAC/C;;GAEC,GACD,MAAM,OAAO,IAAwB,EAAkB;QACrD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,EAAE;QAElD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAQ,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,EAAE;IAEtD;IAEA;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,KAAa,EACb,UAAoC,EACpC,eAKI,CAAC,CAAC,EACN,aAAsB,EACN;QAChB,MAAM,OAA2B;YAC/B;YACA;YACA,aAAa,aAAa,WAAW;YACrC,SAAS,aAAa,OAAO;YAC7B,MAAM,aAAa,IAAI;YACvB,SAAS,aAAa,OAAO;YAC7B;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,iBACJ,KAAa,EACb,UAAoC,EACpC,eAKI,CAAC,CAAC,EACN,aAAsB,EACN;QAChB,MAAM,OAA2B;YAC/B,QAAQ;YACR;YACA,aAAa,aAAa,WAAW;YACrC,SAAS,aAAa,OAAO;YAC7B,MAAM,aAAa,IAAI;YACvB,SAAS,aAAa,OAAO;YAC7B;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,eACJ,SAGE,EACF,YAOC,EACD,aAAsB,EACN;QAChB,MAAM,aAAuC,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAClE,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;YACzB,CAAC;QAED,MAAM,OAA2B;YAC/B,QAAQ,aAAa,MAAM;YAC3B,OAAO,aAAa,KAAK;YACzB,aAAa,aAAa,WAAW;YACrC,SAAS,aAAa,OAAO;YAC7B,MAAM,aAAa,IAAI;YACvB,SAAS,aAAa,OAAO;YAC7B;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,iBACJ,SAAiB,EACjB,QAAgB,EAChB,YAOC,EACD,aAAsB,EACN;QAChB,MAAM,aAAuC;YAAC;gBAC5C;gBACA;YACF;SAAE;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC;YAAC;gBAAE;gBAAW;YAAS;SAAE,EAAE,cAAc;IACtE;IAEA;;GAEC,GACD,MAAM,gBACJ,QAGE,EACF,YAOC,EACD,aAAsB,EACN;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,cAAc;IACrD;IAEA;;GAEC,GACD,MAAM,kBACJ,eAAuB,EACvB,YAOC,EACD,aAAsB,EACN;QAChB,IAAI;YACF,iCAAiC;YACjC,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,gBAAgB,MAAM,gBAAgB,UAAU,CAAC;YAEvD,6CAA6C;YAC7C,MAAM,aAAuC,cAAc,UAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACjF,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;gBACzB,CAAC;YAED,MAAM,OAA2B;gBAC/B,QAAQ,aAAa,MAAM;gBAC3B,OAAO,aAAa,KAAK;gBACzB,aAAa,aAAa,WAAW;gBACrC,SAAS,aAAa,OAAO;gBAC7B,MAAM,aAAa,IAAI;gBACvB,SAAS,aAAa,OAAO;gBAC7B;gBACA;YACF;YAEA,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqB,IAAwB,EAIhD;QACD,IAAI;YACF,mEAAmE;YACnE,MAAM,kBAAkB,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,UAAU;YAEhE,IAAI,CAAC,gBAAgB,KAAK,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,QAAQ,gBAAgB,MAAM;gBAChC;YACF;YAEA,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,CAAC;YAEhC,OAAO;gBACL,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;iBAAyB;YAC7E;QACF;IACF;IAEA;;GAEC,GACD,MAAc,cAAc,UAAoC,EAG7D;QACD,IAAI;YACF,oDAAoD;YACpD,uCAAuC;YACvC,6DAA6D;YAE7D,MAAM,SAAmB,EAAE;YAE3B,KAAK,MAAM,QAAQ,WAAY;gBAC7B,IAAI,KAAK,QAAQ,IAAI,GAAG;oBACtB,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,SAAS,EAAE;gBAC9D;YACF;YAEA,OAAO;gBACL,OAAO,OAAO,MAAM,KAAK;gBACzB,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS;YACvC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,OAAO;gBACP,QAAQ;oBAAC;iBAAsC;YACjD;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAwB,EAAQ;QAC5D,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,wIAAA,CAAA,eAAY,CAAC,YAAY,CAAC,KAAK,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;YACpD,MAAM,IAAI,MAAM;QAClB;QAEA,uBAAuB;QACvB,KAAK,MAAM,QAAQ,KAAK,UAAU,CAAE;YAClC,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,GAAG;gBAC1C,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG;gBACxC,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,2CAA2C;QAC3C,IAAI,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,WAAW,GAAG;YAClE,MAAM,IAAI,MAAM;QAClB;QAEA,uCAAuC;QACvC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI;YAC5C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAa,EAAW;QACjD,kDAAkD;QAClD,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;IACtD;AACF;AAGO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { UpdateOrderStatusRequest } from '../../types/entities';\n\nexport class OrderUpdateService extends BaseService {\n  /**\n   * Update order status\n   */\n  async updateStatus(id: number, statusId: number): Promise<boolean> {\n    this.logApiCall('PATCH', ApiEndpoints.Orders.UpdateStatus(id), { statusId });\n    \n    const data: UpdateOrderStatusRequest = { statusId };\n    \n    return this.handleVoidResponse(\n      this.client.patch(ApiEndpoints.Orders.UpdateStatus(id), data)\n    );\n  }\n\n  /**\n   * Cancel an order\n   */\n  async cancel(id: number): Promise<boolean> {\n    this.logApiCall('PATCH', ApiEndpoints.Orders.Cancel(id));\n    \n    return this.handleVoidResponse(\n      this.client.patch(ApiEndpoints.Orders.Cancel(id))\n    );\n  }\n\n  /**\n   * Mark order as confirmed\n   */\n  async confirm(id: number): Promise<boolean> {\n    return this.updateStatus(id, 2); // Status ID 2 = Confirmed\n  }\n\n  /**\n   * Mark order as processing\n   */\n  async markAsProcessing(id: number): Promise<boolean> {\n    return this.updateStatus(id, 3); // Status ID 3 = Processing\n  }\n\n  /**\n   * Mark order as shipped\n   */\n  async markAsShipped(id: number): Promise<boolean> {\n    return this.updateStatus(id, 4); // Status ID 4 = Shipped\n  }\n\n  /**\n   * Mark order as delivered\n   */\n  async markAsDelivered(id: number): Promise<boolean> {\n    return this.updateStatus(id, 5); // Status ID 5 = Delivered\n  }\n\n  /**\n   * Mark order as returned\n   */\n  async markAsReturned(id: number): Promise<boolean> {\n    return this.updateStatus(id, 7); // Status ID 7 = Returned\n  }\n\n  /**\n   * Mark order as refunded\n   */\n  async markAsRefunded(id: number): Promise<boolean> {\n    return this.updateStatus(id, 8); // Status ID 8 = Refunded\n  }\n\n  /**\n   * Update payment status to completed\n   */\n  async markPaymentCompleted(id: number): Promise<boolean> {\n    return this.updateStatus(id, 10); // Status ID 10 = Payment Completed\n  }\n\n  /**\n   * Update payment status to failed\n   */\n  async markPaymentFailed(id: number): Promise<boolean> {\n    return this.updateStatus(id, 11); // Status ID 11 = Payment Failed\n  }\n\n  /**\n   * Update payment status to refunded\n   */\n  async markPaymentRefunded(id: number): Promise<boolean> {\n    return this.updateStatus(id, 12); // Status ID 12 = Payment Refunded\n  }\n\n  /**\n   * Bulk update order statuses\n   */\n  async bulkUpdateStatus(orderIds: number[], statusId: number): Promise<{\n    success: boolean;\n    updatedCount: number;\n    errors: string[];\n  }> {\n    this.logApiCall('PATCH', 'Bulk Update Order Status', { \n      orderIds, \n      statusId, \n      count: orderIds.length \n    });\n\n    const results = await Promise.allSettled(\n      orderIds.map(id => this.updateStatus(id, statusId))\n    );\n\n    const successful = results.filter(result => \n      result.status === 'fulfilled' && result.value === true\n    ).length;\n\n    const errors = results\n      .filter(result => result.status === 'rejected')\n      .map((result, index) => \n        `Order ${orderIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\n      );\n\n    return {\n      success: successful === orderIds.length,\n      updatedCount: successful,\n      errors\n    };\n  }\n\n  /**\n   * Process order through workflow (pending -> confirmed -> processing)\n   */\n  async processOrder(id: number): Promise<{\n    success: boolean;\n    currentStatus: string;\n    message: string;\n  }> {\n    try {\n      // Get current order status\n      const { orderGetService } = await import('./get');\n      const order = await orderGetService.getById(id);\n\n      switch (order.statusId) {\n        case 1: // Pending\n          await this.confirm(id);\n          return {\n            success: true,\n            currentStatus: 'Confirmed',\n            message: 'Order confirmed successfully'\n          };\n\n        case 2: // Confirmed\n          await this.markAsProcessing(id);\n          return {\n            success: true,\n            currentStatus: 'Processing',\n            message: 'Order moved to processing'\n          };\n\n        case 3: // Processing\n          await this.markAsShipped(id);\n          return {\n            success: true,\n            currentStatus: 'Shipped',\n            message: 'Order marked as shipped'\n          };\n\n        case 4: // Shipped\n          await this.markAsDelivered(id);\n          return {\n            success: true,\n            currentStatus: 'Delivered',\n            message: 'Order marked as delivered'\n          };\n\n        default:\n          return {\n            success: false,\n            currentStatus: order.status.statusName,\n            message: 'Order cannot be processed further'\n          };\n      }\n    } catch (error) {\n      console.error('Error processing order:', error);\n      return {\n        success: false,\n        currentStatus: 'Unknown',\n        message: error instanceof Error ? error.message : 'Failed to process order'\n      };\n    }\n  }\n\n  /**\n   * Reverse order status (for corrections)\n   */\n  async reverseStatus(id: number): Promise<{\n    success: boolean;\n    previousStatus: string;\n    message: string;\n  }> {\n    try {\n      const { orderGetService } = await import('./get');\n      const order = await orderGetService.getById(id);\n\n      let newStatusId: number;\n      let statusName: string;\n\n      switch (order.statusId) {\n        case 5: // Delivered -> Shipped\n          newStatusId = 4;\n          statusName = 'Shipped';\n          break;\n\n        case 4: // Shipped -> Processing\n          newStatusId = 3;\n          statusName = 'Processing';\n          break;\n\n        case 3: // Processing -> Confirmed\n          newStatusId = 2;\n          statusName = 'Confirmed';\n          break;\n\n        case 2: // Confirmed -> Pending\n          newStatusId = 1;\n          statusName = 'Pending';\n          break;\n\n        default:\n          return {\n            success: false,\n            previousStatus: order.status.statusName,\n            message: 'Order status cannot be reversed'\n          };\n      }\n\n      await this.updateStatus(id, newStatusId);\n\n      return {\n        success: true,\n        previousStatus: statusName,\n        message: `Order status reversed to ${statusName}`\n      };\n    } catch (error) {\n      console.error('Error reversing order status:', error);\n      return {\n        success: false,\n        previousStatus: 'Unknown',\n        message: error instanceof Error ? error.message : 'Failed to reverse order status'\n      };\n    }\n  }\n\n  /**\n   * Check if status update is valid\n   */\n  async canUpdateStatus(id: number, newStatusId: number): Promise<{\n    canUpdate: boolean;\n    reason?: string;\n    currentStatus?: string;\n  }> {\n    try {\n      const { orderGetService } = await import('./get');\n      const order = await orderGetService.getById(id);\n\n      // Define valid status transitions\n      const validTransitions: Record<number, number[]> = {\n        1: [2, 6], // Pending -> Confirmed, Cancelled\n        2: [3, 6], // Confirmed -> Processing, Cancelled\n        3: [4, 6], // Processing -> Shipped, Cancelled\n        4: [5, 7], // Shipped -> Delivered, Returned\n        5: [7],    // Delivered -> Returned\n        6: [],     // Cancelled (final)\n        7: [8],    // Returned -> Refunded\n        8: []      // Refunded (final)\n      };\n\n      const allowedStatuses = validTransitions[order.statusId] || [];\n      const canUpdate = allowedStatuses.includes(newStatusId);\n\n      return {\n        canUpdate,\n        currentStatus: order.status.statusName,\n        reason: canUpdate ? undefined : 'Invalid status transition'\n      };\n    } catch (error) {\n      console.error('Error checking status update validity:', error);\n      return {\n        canUpdate: false,\n        reason: 'Error checking order status'\n      };\n    }\n  }\n\n  /**\n   * Safe status update with validation\n   */\n  async safeUpdateStatus(id: number, statusId: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const validation = await this.canUpdateStatus(id, statusId);\n\n      if (!validation.canUpdate) {\n        return {\n          success: false,\n          message: validation.reason || 'Status update not allowed'\n        };\n      }\n\n      const updated = await this.updateStatus(id, statusId);\n\n      return {\n        success: updated,\n        message: updated ? 'Order status updated successfully' : 'Failed to update order status'\n      };\n    } catch (error) {\n      console.error('Error in safe status update:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\n      };\n    }\n  }\n}\n\n// Export singleton instance\nexport const orderUpdateService = new OrderUpdateService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,2BAA2B,wIAAA,CAAA,cAAW;IACjD;;GAEC,GACD,MAAM,aAAa,EAAU,EAAE,QAAgB,EAAoB;QACjE,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK;YAAE;QAAS;QAE1E,MAAM,OAAiC;YAAE;QAAS;QAElD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK;IAE5D;IAEA;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAC;QAEpD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAC;IAEjD;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAoB;QAC1C,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,0BAA0B;IAC7D;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAoB;QACnD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,2BAA2B;IAC9D;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAoB;QAChD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,wBAAwB;IAC3D;IAEA;;GAEC,GACD,MAAM,gBAAgB,EAAU,EAAoB;QAClD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,0BAA0B;IAC7D;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAoB;QACjD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,yBAAyB;IAC5D;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAoB;QACjD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,yBAAyB;IAC5D;IAEA;;GAEC,GACD,MAAM,qBAAqB,EAAU,EAAoB;QACvD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,mCAAmC;IACvE;IAEA;;GAEC,GACD,MAAM,kBAAkB,EAAU,EAAoB;QACpD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,gCAAgC;IACpE;IAEA;;GAEC,GACD,MAAM,oBAAoB,EAAU,EAAoB;QACtD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,kCAAkC;IACtE;IAEA;;GAEC,GACD,MAAM,iBAAiB,QAAkB,EAAE,QAAgB,EAIxD;QACD,IAAI,CAAC,UAAU,CAAC,SAAS,4BAA4B;YACnD;YACA;YACA,OAAO,SAAS,MAAM;QACxB;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,SAAS,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,YAAY,CAAC,IAAI;QAG3C,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,KAAK,MAClD,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAGjG,OAAO;YACL,SAAS,eAAe,SAAS,MAAM;YACvC,cAAc;YACd;QACF;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,EAAU,EAI1B;QACD,IAAI;YACF,2BAA2B;YAC3B,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,QAAQ,MAAM,gBAAgB,OAAO,CAAC;YAE5C,OAAQ,MAAM,QAAQ;gBACpB,KAAK;oBACH,MAAM,IAAI,CAAC,OAAO,CAAC;oBACnB,OAAO;wBACL,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBAEF,KAAK;oBACH,MAAM,IAAI,CAAC,gBAAgB,CAAC;oBAC5B,OAAO;wBACL,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBAEF,KAAK;oBACH,MAAM,IAAI,CAAC,aAAa,CAAC;oBACzB,OAAO;wBACL,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBAEF,KAAK;oBACH,MAAM,IAAI,CAAC,eAAe,CAAC;oBAC3B,OAAO;wBACL,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBAEF;oBACE,OAAO;wBACL,SAAS;wBACT,eAAe,MAAM,MAAM,CAAC,UAAU;wBACtC,SAAS;oBACX;YACJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,eAAe;gBACf,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAI3B;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,QAAQ,MAAM,gBAAgB,OAAO,CAAC;YAE5C,IAAI;YACJ,IAAI;YAEJ,OAAQ,MAAM,QAAQ;gBACpB,KAAK;oBACH,cAAc;oBACd,aAAa;oBACb;gBAEF,KAAK;oBACH,cAAc;oBACd,aAAa;oBACb;gBAEF,KAAK;oBACH,cAAc;oBACd,aAAa;oBACb;gBAEF,KAAK;oBACH,cAAc;oBACd,aAAa;oBACb;gBAEF;oBACE,OAAO;wBACL,SAAS;wBACT,gBAAgB,MAAM,MAAM,CAAC,UAAU;wBACvC,SAAS;oBACX;YACJ;YAEA,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI;YAE5B,OAAO;gBACL,SAAS;gBACT,gBAAgB;gBAChB,SAAS,CAAC,yBAAyB,EAAE,YAAY;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBACL,SAAS;gBACT,gBAAgB;gBAChB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,EAAU,EAAE,WAAmB,EAIlD;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,QAAQ,MAAM,gBAAgB,OAAO,CAAC;YAE5C,kCAAkC;YAClC,MAAM,mBAA6C;gBACjD,GAAG;oBAAC;oBAAG;iBAAE;gBACT,GAAG;oBAAC;oBAAG;iBAAE;gBACT,GAAG;oBAAC;oBAAG;iBAAE;gBACT,GAAG;oBAAC;oBAAG;iBAAE;gBACT,GAAG;oBAAC;iBAAE;gBACN,GAAG,EAAE;gBACL,GAAG;oBAAC;iBAAE;gBACN,GAAG,EAAE,CAAM,mBAAmB;YAChC;YAEA,MAAM,kBAAkB,gBAAgB,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE;YAC9D,MAAM,YAAY,gBAAgB,QAAQ,CAAC;YAE3C,OAAO;gBACL;gBACA,eAAe,MAAM,MAAM,CAAC,UAAU;gBACtC,QAAQ,YAAY,YAAY;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAE,QAAgB,EAGhD;QACD,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI;YAElD,IAAI,CAAC,WAAW,SAAS,EAAE;gBACzB,OAAO;oBACL,SAAS;oBACT,SAAS,WAAW,MAAM,IAAI;gBAChC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI;YAE5C,OAAO;gBACL,SAAS;gBACT,SAAS,UAAU,sCAAsC;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 1910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/delete.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\n\nexport class OrderDeleteService extends BaseService {\n  /**\n   * Delete an order by ID\n   */\n  async delete(id: number): Promise<boolean> {\n    this.logApiCall('DELETE', ApiEndpoints.Orders.ById(id));\n    \n    return this.handleVoidResponse(\n      this.client.delete(ApiEndpoints.Orders.ById(id))\n    );\n  }\n\n  /**\n   * Delete multiple orders\n   */\n  async deleteBatch(ids: number[]): Promise<boolean[]> {\n    this.logApiCall('DELETE', 'Batch Orders', { count: ids.length });\n    \n    const promises = ids.map(id => this.delete(id));\n    return Promise.all(promises);\n  }\n\n  /**\n   * Check if order can be safely deleted\n   */\n  async canDelete(id: number): Promise<{\n    canDelete: boolean;\n    reason?: string;\n    orderStatus?: string;\n  }> {\n    try {\n      const { orderGetService } = await import('./get');\n      const order = await orderGetService.getById(id);\n\n      // Orders can typically only be deleted if they are:\n      // - Pending (status 1)\n      // - Cancelled (status 6)\n      // - In some cases, failed payment orders\n      const deletableStatuses = [1, 6, 11]; // Pending, Cancelled, Payment Failed\n\n      const canDelete = deletableStatuses.includes(order.statusId);\n      \n      let reason: string | undefined;\n      if (!canDelete) {\n        switch (order.statusId) {\n          case 2:\n          case 3:\n          case 4:\n            reason = 'Cannot delete confirmed or processing orders';\n            break;\n          case 5:\n            reason = 'Cannot delete delivered orders';\n            break;\n          case 7:\n          case 8:\n            reason = 'Cannot delete returned or refunded orders';\n            break;\n          default:\n            reason = 'Order cannot be deleted in current status';\n        }\n      }\n\n      return {\n        canDelete,\n        reason,\n        orderStatus: order.status.statusName\n      };\n    } catch (error) {\n      console.error('Error checking if order can be deleted:', error);\n      return {\n        canDelete: false,\n        reason: 'Error checking order status'\n      };\n    }\n  }\n\n  /**\n   * Safe delete - checks if order can be deleted first\n   */\n  async safeDelete(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const deleteCheck = await this.canDelete(id);\n      \n      if (!deleteCheck.canDelete) {\n        return {\n          success: false,\n          message: deleteCheck.reason || 'Order cannot be deleted'\n        };\n      }\n\n      const deleted = await this.delete(id);\n      \n      if (deleted) {\n        return {\n          success: true,\n          message: 'Order deleted successfully'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Failed to delete order'\n        };\n      }\n    } catch (error) {\n      console.error('Error during safe delete:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\n      };\n    }\n  }\n\n  /**\n   * Cancel order instead of deleting (safer option)\n   */\n  async cancelInsteadOfDelete(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const { orderUpdateService } = await import('./update');\n      const cancelled = await orderUpdateService.cancel(id);\n      \n      return {\n        success: cancelled,\n        message: cancelled \n          ? 'Order cancelled successfully' \n          : 'Failed to cancel order'\n      };\n    } catch (error) {\n      console.error('Error cancelling order:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to cancel order'\n      };\n    }\n  }\n\n  /**\n   * Delete orders by status (bulk operation)\n   */\n  async deleteByStatus(statusId: number): Promise<{\n    success: boolean;\n    message: string;\n    deletedCount: number;\n    totalCount: number;\n  }> {\n    try {\n      const { orderGetService } = await import('./get');\n      const orders = await orderGetService.getByStatus(statusId);\n      \n      if (orders.length === 0) {\n        return {\n          success: true,\n          message: 'No orders found with specified status',\n          deletedCount: 0,\n          totalCount: 0\n        };\n      }\n\n      // Check if any orders can be deleted\n      const deletableOrders: number[] = [];\n      for (const order of orders) {\n        const canDelete = await this.canDelete(order.id);\n        if (canDelete.canDelete) {\n          deletableOrders.push(order.id);\n        }\n      }\n\n      if (deletableOrders.length === 0) {\n        return {\n          success: false,\n          message: 'No orders can be deleted in current status',\n          deletedCount: 0,\n          totalCount: orders.length\n        };\n      }\n\n      const results = await this.deleteBatch(deletableOrders);\n      const deletedCount = results.filter(result => result).length;\n      \n      return {\n        success: deletedCount === deletableOrders.length,\n        message: `Deleted ${deletedCount} of ${deletableOrders.length} eligible orders`,\n        deletedCount,\n        totalCount: orders.length\n      };\n    } catch (error) {\n      console.error('Error deleting orders by status:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to delete orders',\n        deletedCount: 0,\n        totalCount: 0\n      };\n    }\n  }\n\n  /**\n   * Delete old cancelled orders (cleanup operation)\n   */\n  async deleteOldCancelledOrders(olderThanDays: number = 30): Promise<{\n    success: boolean;\n    message: string;\n    deletedCount: number;\n  }> {\n    try {\n      const { orderGetService } = await import('./get');\n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);\n\n      // Get cancelled orders\n      const cancelledOrders = await orderGetService.getByStatus(6); // Status 6 = Cancelled\n      \n      // Filter orders older than cutoff date\n      const oldOrders = cancelledOrders.filter(order => \n        new Date(order.createdAt) < cutoffDate\n      );\n\n      if (oldOrders.length === 0) {\n        return {\n          success: true,\n          message: `No cancelled orders older than ${olderThanDays} days found`,\n          deletedCount: 0\n        };\n      }\n\n      const orderIds = oldOrders.map(order => order.id);\n      const results = await this.deleteBatch(orderIds);\n      const deletedCount = results.filter(result => result).length;\n      \n      return {\n        success: deletedCount === oldOrders.length,\n        message: `Deleted ${deletedCount} old cancelled orders`,\n        deletedCount\n      };\n    } catch (error) {\n      console.error('Error deleting old cancelled orders:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to delete old orders',\n        deletedCount: 0\n      };\n    }\n  }\n\n  /**\n   * Archive order instead of deleting (move to archive status)\n   */\n  async archive(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      // This would require adding an \"Archived\" status to the system\n      // For now, we'll use the existing status system\n      const { orderUpdateService } = await import('./update');\n      \n      // Check if order can be archived (similar rules to deletion)\n      const canDelete = await this.canDelete(id);\n      if (!canDelete.canDelete) {\n        return {\n          success: false,\n          message: canDelete.reason || 'Order cannot be archived'\n        };\n      }\n\n      // In a real system, you might have a specific \"Archived\" status\n      // For now, we'll just mark it as cancelled\n      const archived = await orderUpdateService.cancel(id);\n      \n      return {\n        success: archived,\n        message: archived \n          ? 'Order archived successfully' \n          : 'Failed to archive order'\n      };\n    } catch (error) {\n      console.error('Error archiving order:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to archive order'\n      };\n    }\n  }\n}\n\n// Export singleton instance\nexport const orderDeleteService = new OrderDeleteService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,2BAA2B,wIAAA,CAAA,cAAW;IACjD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,CAAC;QAEnD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,CAAC;IAEhD;IAEA;;GAEC,GACD,MAAM,YAAY,GAAa,EAAsB;QACnD,IAAI,CAAC,UAAU,CAAC,UAAU,gBAAgB;YAAE,OAAO,IAAI,MAAM;QAAC;QAE9D,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,MAAM,CAAC;QAC3C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAIvB;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,QAAQ,MAAM,gBAAgB,OAAO,CAAC;YAE5C,oDAAoD;YACpD,uBAAuB;YACvB,yBAAyB;YACzB,yCAAyC;YACzC,MAAM,oBAAoB;gBAAC;gBAAG;gBAAG;aAAG,EAAE,qCAAqC;YAE3E,MAAM,YAAY,kBAAkB,QAAQ,CAAC,MAAM,QAAQ;YAE3D,IAAI;YACJ,IAAI,CAAC,WAAW;gBACd,OAAQ,MAAM,QAAQ;oBACpB,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;oBACL,KAAK;wBACH,SAAS;wBACT;oBACF;wBACE,SAAS;gBACb;YACF;YAEA,OAAO;gBACL;gBACA;gBACA,aAAa,MAAM,MAAM,CAAC,UAAU;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAGxB;QACD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC;YAEzC,IAAI,CAAC,YAAY,SAAS,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,YAAY,MAAM,IAAI;gBACjC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAElC,IAAI,SAAS;gBACX,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,EAAU,EAGnC;QACD,IAAI;YACF,MAAM,EAAE,kBAAkB,EAAE,GAAG;YAC/B,MAAM,YAAY,MAAM,mBAAmB,MAAM,CAAC;YAElD,OAAO;gBACL,SAAS;gBACT,SAAS,YACL,iCACA;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,QAAgB,EAKlC;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,SAAS,MAAM,gBAAgB,WAAW,CAAC;YAEjD,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;oBACd,YAAY;gBACd;YACF;YAEA,qCAAqC;YACrC,MAAM,kBAA4B,EAAE;YACpC,KAAK,MAAM,SAAS,OAAQ;gBAC1B,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC/C,IAAI,UAAU,SAAS,EAAE;oBACvB,gBAAgB,IAAI,CAAC,MAAM,EAAE;gBAC/B;YACF;YAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;gBAChC,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;oBACd,YAAY,OAAO,MAAM;gBAC3B;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,gBAAgB,MAAM;gBAChD,SAAS,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,gBAAgB,MAAM,CAAC,gBAAgB,CAAC;gBAC/E;gBACA,YAAY,OAAO,MAAM;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;gBACd,YAAY;YACd;QACF;IACF;IAEA;;GAEC,GACD,MAAM,yBAAyB,gBAAwB,EAAE,EAItD;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,uBAAuB;YACvB,MAAM,kBAAkB,MAAM,gBAAgB,WAAW,CAAC,IAAI,uBAAuB;YAErF,uCAAuC;YACvC,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAA,QACvC,IAAI,KAAK,MAAM,SAAS,IAAI;YAG9B,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,+BAA+B,EAAE,cAAc,WAAW,CAAC;oBACrE,cAAc;gBAChB;YACF;YAEA,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE;YAChD,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,UAAU,MAAM;gBAC1C,SAAS,CAAC,QAAQ,EAAE,aAAa,qBAAqB,CAAC;gBACvD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAGrB;QACD,IAAI;YACF,+DAA+D;YAC/D,gDAAgD;YAChD,MAAM,EAAE,kBAAkB,EAAE,GAAG;YAE/B,6DAA6D;YAC7D,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC;YACvC,IAAI,CAAC,UAAU,SAAS,EAAE;gBACxB,OAAO;oBACL,SAAS;oBACT,SAAS,UAAU,MAAM,IAAI;gBAC/B;YACF;YAEA,gEAAgE;YAChE,2CAA2C;YAC3C,MAAM,WAAW,MAAM,mBAAmB,MAAM,CAAC;YAEjD,OAAO;gBACL,SAAS;gBACT,SAAS,WACL,gCACA;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 2152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/index.ts"], "sourcesContent": ["// Order Services\nexport { OrderGetService, orderGetService } from './get';\nexport { OrderPostService, orderPostService } from './post';\nexport { OrderUpdateService, orderUpdateService } from './update';\nexport { OrderDeleteService, orderDeleteService } from './delete';\n\n// Combined Order Service\nimport { orderGetService } from './get';\nimport { orderPostService } from './post';\nimport { orderUpdateService } from './update';\nimport { orderDeleteService } from './delete';\n\nexport class OrderService {\n  get = orderGetService;\n  post = orderPostService;\n  update = orderUpdateService;\n  delete = orderDeleteService;\n}\n\n// Export singleton instance\nexport const orderService = new OrderService();\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;;AACjB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,uIAAA,CAAA,kBAAe,CAAC;IACtB,OAAO,wIAAA,CAAA,mBAAgB,CAAC;IACxB,SAAS,0IAAA,CAAA,qBAAkB,CAAC;IAC5B,SAAS,0IAAA,CAAA,qBAAkB,CAAC;AAC9B;AAGO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 2194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/post.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { User, CreateUserRequest } from '../../types/entities';\n\nexport class UserPostService extends BaseService {\n  /**\n   * Create a new user\n   */\n  async create(data: CreateUserRequest): Promise<User> {\n    this.logApiCall('POST', ApiEndpoints.Users.Base, { ...data, password: '[HIDDEN]' });\n    \n    // Validate required fields\n    this.validateCreateRequest(data);\n    \n    return this.handleResponse(\n      this.client.post<User>(ApiEndpoints.Users.Base, data)\n    );\n  }\n\n  /**\n   * Create a customer user\n   */\n  async createCustomer(\n    email: string,\n    password: string,\n    name?: string,\n    phoneNumber?: string,\n    address?: {\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    }\n  ): Promise<User> {\n    const data: CreateUserRequest = {\n      role: 'customer',\n      email,\n      password,\n      name,\n      phoneNumber,\n      country: address?.country,\n      city: address?.city,\n      zipCode: address?.zipCode,\n      active: true\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Create an admin user\n   */\n  async createAdmin(\n    email: string,\n    password: string,\n    name?: string,\n    phoneNumber?: string\n  ): Promise<User> {\n    const data: CreateUserRequest = {\n      role: 'admin',\n      email,\n      password,\n      name,\n      phoneNumber,\n      active: true\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Register a new customer (public registration)\n   */\n  async register(\n    email: string,\n    password: string,\n    name?: string,\n    phoneNumber?: string\n  ): Promise<User> {\n    // Check if email already exists\n    const { userGetService } = await import('./get');\n    const emailExists = await userGetService.emailExists(email);\n    \n    if (emailExists) {\n      throw new Error('Email already exists');\n    }\n\n    return this.createCustomer(email, password, name, phoneNumber);\n  }\n\n  /**\n   * Create guest user (for guest checkout)\n   */\n  async createGuest(email: string): Promise<User> {\n    const data: CreateUserRequest = {\n      role: 'guest',\n      email,\n      password: this.generateRandomPassword(), // Generate temporary password\n      active: true\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Create multiple users in batch\n   */\n  async createBatch(users: CreateUserRequest[]): Promise<User[]> {\n    this.logApiCall('POST', 'Batch Users', { count: users.length });\n    \n    const promises = users.map(user => this.create(user));\n    return Promise.all(promises);\n  }\n\n  /**\n   * Import users from CSV data\n   */\n  async importFromCsv(csvData: Array<{\n    email: string;\n    name?: string;\n    role?: string;\n    phoneNumber?: string;\n    country?: string;\n    city?: string;\n    zipCode?: string;\n  }>): Promise<{\n    success: boolean;\n    imported: number;\n    failed: number;\n    errors: string[];\n  }> {\n    const results = {\n      success: true,\n      imported: 0,\n      failed: 0,\n      errors: [] as string[]\n    };\n\n    for (const [index, userData] of csvData.entries()) {\n      try {\n        const createData: CreateUserRequest = {\n          role: userData.role || 'customer',\n          email: userData.email,\n          password: this.generateRandomPassword(),\n          name: userData.name,\n          phoneNumber: userData.phoneNumber,\n          country: userData.country,\n          city: userData.city,\n          zipCode: userData.zipCode,\n          active: true\n        };\n\n        await this.create(createData);\n        results.imported++;\n      } catch (error) {\n        results.failed++;\n        results.errors.push(\n          `Row ${index + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`\n        );\n      }\n    }\n\n    results.success = results.failed === 0;\n    return results;\n  }\n\n  /**\n   * Create user with email verification\n   */\n  async createWithVerification(data: CreateUserRequest): Promise<{\n    user: User;\n    verificationRequired: boolean;\n    verificationToken?: string;\n  }> {\n    try {\n      const user = await this.create(data);\n      \n      // In a real application, you would:\n      // 1. Generate a verification token\n      // 2. Send verification email\n      // 3. Set user as inactive until verified\n      \n      return {\n        user,\n        verificationRequired: true,\n        verificationToken: this.generateVerificationToken()\n      };\n    } catch (error) {\n      console.error('Error creating user with verification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create user with social login data\n   */\n  async createFromSocialLogin(\n    provider: 'google' | 'facebook' | 'apple',\n    socialId: string,\n    email: string,\n    name?: string\n  ): Promise<User> {\n    const data: CreateUserRequest = {\n      role: 'customer',\n      email,\n      password: this.generateRandomPassword(), // Social users don't need password\n      name,\n      active: true\n    };\n\n    // In a real application, you would also store the social provider info\n    return this.create(data);\n  }\n\n  /**\n   * Generate random password for system-created users\n   */\n  private generateRandomPassword(): string {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';\n    let password = '';\n    for (let i = 0; i < 12; i++) {\n      password += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return password;\n  }\n\n  /**\n   * Generate verification token\n   */\n  private generateVerificationToken(): string {\n    return Math.random().toString(36).substring(2, 15) + \n           Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Validate create user request\n   */\n  private validateCreateRequest(data: CreateUserRequest): void {\n    if (!data.email || !ServiceUtils.isValidEmail(data.email)) {\n      throw new Error('Valid email is required');\n    }\n\n    if (!data.password || data.password.length < 6) {\n      throw new Error('Password must be at least 6 characters long');\n    }\n\n    if (!data.role || !['admin', 'customer', 'guest'].includes(data.role)) {\n      throw new Error('Valid role is required (admin, customer, or guest)');\n    }\n\n    // Validate phone number format if provided\n    if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {\n      throw new Error('Invalid phone number format');\n    }\n\n    // Validate name length if provided\n    if (data.name && data.name.length > 100) {\n      throw new Error('Name must be 100 characters or less');\n    }\n\n    // Validate address fields if provided\n    if (data.country && data.country.length > 100) {\n      throw new Error('Country must be 100 characters or less');\n    }\n\n    if (data.city && data.city.length > 100) {\n      throw new Error('City must be 100 characters or less');\n    }\n\n    if (data.zipCode && data.zipCode.length > 20) {\n      throw new Error('Zip code must be 20 characters or less');\n    }\n\n    // Password strength validation\n    if (!this.isStrongPassword(data.password)) {\n      throw new Error('Password must contain at least one uppercase letter, one lowercase letter, and one number');\n    }\n  }\n\n  /**\n   * Validate phone number format\n   */\n  private isValidPhoneNumber(phone: string): boolean {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n  }\n\n  /**\n   * Check password strength\n   */\n  private isStrongPassword(password: string): boolean {\n    const hasUpperCase = /[A-Z]/.test(password);\n    const hasLowerCase = /[a-z]/.test(password);\n    const hasNumbers = /\\d/.test(password);\n    \n    return hasUpperCase && hasLowerCase && hasNumbers;\n  }\n}\n\n// Export singleton instance\nexport const userPostService = new UserPostService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,wBAAwB,wIAAA,CAAA,cAAW;IAC9C;;GAEC,GACD,MAAM,OAAO,IAAuB,EAAiB;QACnD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,EAAE;YAAE,GAAG,IAAI;YAAE,UAAU;QAAW;QAEjF,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,EAAE;IAEpD;IAEA;;GAEC,GACD,MAAM,eACJ,KAAa,EACb,QAAgB,EAChB,IAAa,EACb,WAAoB,EACpB,OAIC,EACc;QACf,MAAM,OAA0B;YAC9B,MAAM;YACN;YACA;YACA;YACA;YACA,SAAS,SAAS;YAClB,MAAM,SAAS;YACf,SAAS,SAAS;YAClB,QAAQ;QACV;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YACJ,KAAa,EACb,QAAgB,EAChB,IAAa,EACb,WAAoB,EACL;QACf,MAAM,OAA0B;YAC9B,MAAM;YACN;YACA;YACA;YACA;YACA,QAAQ;QACV;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,SACJ,KAAa,EACb,QAAgB,EAChB,IAAa,EACb,WAAoB,EACL;QACf,gCAAgC;QAChC,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,MAAM,cAAc,MAAM,eAAe,WAAW,CAAC;QAErD,IAAI,aAAa;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,UAAU,MAAM;IACpD;IAEA;;GAEC,GACD,MAAM,YAAY,KAAa,EAAiB;QAC9C,MAAM,OAA0B;YAC9B,MAAM;YACN;YACA,UAAU,IAAI,CAAC,sBAAsB;YACrC,QAAQ;QACV;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YAAY,KAA0B,EAAmB;QAC7D,IAAI,CAAC,UAAU,CAAC,QAAQ,eAAe;YAAE,OAAO,MAAM,MAAM;QAAC;QAE7D,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,MAAM,CAAC;QAC/C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,cAAc,OAQlB,EAKC;QACD,MAAM,UAAU;YACd,SAAS;YACT,UAAU;YACV,QAAQ;YACR,QAAQ,EAAE;QACZ;QAEA,KAAK,MAAM,CAAC,OAAO,SAAS,IAAI,QAAQ,OAAO,GAAI;YACjD,IAAI;gBACF,MAAM,aAAgC;oBACpC,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,SAAS,KAAK;oBACrB,UAAU,IAAI,CAAC,sBAAsB;oBACrC,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;oBACjC,SAAS,SAAS,OAAO;oBACzB,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,QAAQ;gBACV;gBAEA,MAAM,IAAI,CAAC,MAAM,CAAC;gBAClB,QAAQ,QAAQ;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,MAAM;gBACd,QAAQ,MAAM,CAAC,IAAI,CACjB,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAEnF;QACF;QAEA,QAAQ,OAAO,GAAG,QAAQ,MAAM,KAAK;QACrC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,uBAAuB,IAAuB,EAIjD;QACD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC;YAE/B,oCAAoC;YACpC,mCAAmC;YACnC,6BAA6B;YAC7B,yCAAyC;YAEzC,OAAO;gBACL;gBACA,sBAAsB;gBACtB,mBAAmB,IAAI,CAAC,yBAAyB;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBACJ,QAAyC,EACzC,QAAgB,EAChB,KAAa,EACb,IAAa,EACE;QACf,MAAM,OAA0B;YAC9B,MAAM;YACN;YACA,UAAU,IAAI,CAAC,sBAAsB;YACrC;YACA,QAAQ;QACV;QAEA,uEAAuE;QACvE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,AAAQ,yBAAiC;QACvC,MAAM,QAAQ;QACd,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,YAAY,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;QAClE;QACA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,4BAAoC;QAC1C,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACjD;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAuB,EAAQ;QAC3D,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,wIAAA,CAAA,eAAY,CAAC,YAAY,CAAC,KAAK,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;YAAC;YAAS;YAAY;SAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrE,MAAM,IAAI,MAAM;QAClB;QAEA,2CAA2C;QAC3C,IAAI,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,WAAW,GAAG;YAClE,MAAM,IAAI,MAAM;QAClB;QAEA,mCAAmC;QACnC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK;YAC7C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,QAAQ,GAAG;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAa,EAAW;QACjD,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;IACtD;IAEA;;GAEC,GACD,AAAQ,iBAAiB,QAAgB,EAAW;QAClD,MAAM,eAAe,QAAQ,IAAI,CAAC;QAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;QAClC,MAAM,aAAa,KAAK,IAAI,CAAC;QAE7B,OAAO,gBAAgB,gBAAgB;IACzC;AACF;AAGO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 2414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { User, UpdateUserRequest } from '../../types/entities';\n\nexport class UserUpdateService extends BaseService {\n  /**\n   * Update an existing user\n   */\n  async update(id: number, data: UpdateUserRequest): Promise<User> {\n    this.logApiCall('PUT', ApiEndpoints.Users.ById(id), data);\n    \n    // Validate required fields\n    this.validateUpdateRequest(data);\n    \n    return this.handleResponse(\n      this.client.put<User>(ApiEndpoints.Users.ById(id), data)\n    );\n  }\n\n  /**\n   * Activate a user\n   */\n  async activate(id: number): Promise<boolean> {\n    this.logApiCall('PATCH', ApiEndpoints.Users.Activate(id));\n    \n    return this.handleVoidResponse(\n      this.client.patch(ApiEndpoints.Users.Activate(id))\n    );\n  }\n\n  /**\n   * Deactivate a user\n   */\n  async deactivate(id: number): Promise<boolean> {\n    this.logApiCall('PATCH', ApiEndpoints.Users.Deactivate(id));\n    \n    return this.handleVoidResponse(\n      this.client.patch(ApiEndpoints.Users.Deactivate(id))\n    );\n  }\n\n  /**\n   * Update user profile information\n   */\n  async updateProfile(\n    id: number,\n    name?: string,\n    phoneNumber?: string,\n    address?: {\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    }\n  ): Promise<User> {\n    const currentUser = await this.getCurrentUser(id);\n    \n    const data: UpdateUserRequest = {\n      role: currentUser.role,\n      name: name ?? currentUser.name,\n      phoneNumber: phoneNumber ?? currentUser.phoneNumber,\n      country: address?.country ?? currentUser.country,\n      city: address?.city ?? currentUser.city,\n      zipCode: address?.zipCode ?? currentUser.zipCode,\n      active: currentUser.active\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Update user contact information\n   */\n  async updateContact(\n    id: number,\n    phoneNumber?: string,\n    address?: {\n      country?: string;\n      city?: string;\n      zipCode?: string;\n    }\n  ): Promise<User> {\n    const currentUser = await this.getCurrentUser(id);\n    \n    const data: UpdateUserRequest = {\n      role: currentUser.role,\n      name: currentUser.name,\n      phoneNumber: phoneNumber ?? currentUser.phoneNumber,\n      country: address?.country ?? currentUser.country,\n      city: address?.city ?? currentUser.city,\n      zipCode: address?.zipCode ?? currentUser.zipCode,\n      active: currentUser.active\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Update user role\n   */\n  async updateRole(id: number, newRole: string): Promise<User> {\n    if (!['admin', 'customer', 'guest'].includes(newRole)) {\n      throw new Error('Invalid role. Must be admin, customer, or guest');\n    }\n\n    const currentUser = await this.getCurrentUser(id);\n    \n    const data: UpdateUserRequest = {\n      role: newRole,\n      name: currentUser.name,\n      phoneNumber: currentUser.phoneNumber,\n      country: currentUser.country,\n      city: currentUser.city,\n      zipCode: currentUser.zipCode,\n      active: currentUser.active\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Update user address\n   */\n  async updateAddress(\n    id: number,\n    country?: string,\n    city?: string,\n    zipCode?: string\n  ): Promise<User> {\n    const currentUser = await this.getCurrentUser(id);\n    \n    const data: UpdateUserRequest = {\n      role: currentUser.role,\n      name: currentUser.name,\n      phoneNumber: currentUser.phoneNumber,\n      country: country ?? currentUser.country,\n      city: city ?? currentUser.city,\n      zipCode: zipCode ?? currentUser.zipCode,\n      active: currentUser.active\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Toggle user active status\n   */\n  async toggleActiveStatus(id: number): Promise<User> {\n    const currentUser = await this.getCurrentUser(id);\n    \n    if (currentUser.active) {\n      await this.deactivate(id);\n    } else {\n      await this.activate(id);\n    }\n\n    // Return updated user\n    return this.getCurrentUser(id);\n  }\n\n  /**\n   * Bulk update user roles\n   */\n  async bulkUpdateRole(userIds: number[], newRole: string): Promise<{\n    success: boolean;\n    updatedCount: number;\n    errors: string[];\n  }> {\n    this.logApiCall('PUT', 'Bulk Update User Roles', { \n      userIds, \n      newRole, \n      count: userIds.length \n    });\n\n    const results = await Promise.allSettled(\n      userIds.map(id => this.updateRole(id, newRole))\n    );\n\n    const successful = results.filter(result => \n      result.status === 'fulfilled'\n    ).length;\n\n    const errors = results\n      .filter(result => result.status === 'rejected')\n      .map((result, index) => \n        `User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\n      );\n\n    return {\n      success: successful === userIds.length,\n      updatedCount: successful,\n      errors\n    };\n  }\n\n  /**\n   * Bulk activate users\n   */\n  async bulkActivate(userIds: number[]): Promise<{\n    success: boolean;\n    activatedCount: number;\n    errors: string[];\n  }> {\n    this.logApiCall('PATCH', 'Bulk Activate Users', { \n      userIds, \n      count: userIds.length \n    });\n\n    const results = await Promise.allSettled(\n      userIds.map(id => this.activate(id))\n    );\n\n    const successful = results.filter(result => \n      result.status === 'fulfilled' && result.value === true\n    ).length;\n\n    const errors = results\n      .filter(result => result.status === 'rejected')\n      .map((result, index) => \n        `User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\n      );\n\n    return {\n      success: successful === userIds.length,\n      activatedCount: successful,\n      errors\n    };\n  }\n\n  /**\n   * Bulk deactivate users\n   */\n  async bulkDeactivate(userIds: number[]): Promise<{\n    success: boolean;\n    deactivatedCount: number;\n    errors: string[];\n  }> {\n    this.logApiCall('PATCH', 'Bulk Deactivate Users', { \n      userIds, \n      count: userIds.length \n    });\n\n    const results = await Promise.allSettled(\n      userIds.map(id => this.deactivate(id))\n    );\n\n    const successful = results.filter(result => \n      result.status === 'fulfilled' && result.value === true\n    ).length;\n\n    const errors = results\n      .filter(result => result.status === 'rejected')\n      .map((result, index) => \n        `User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\n      );\n\n    return {\n      success: successful === userIds.length,\n      deactivatedCount: successful,\n      errors\n    };\n  }\n\n  /**\n   * Promote user to admin\n   */\n  async promoteToAdmin(id: number): Promise<User> {\n    return this.updateRole(id, 'admin');\n  }\n\n  /**\n   * Demote admin to customer\n   */\n  async demoteToCustomer(id: number): Promise<User> {\n    return this.updateRole(id, 'customer');\n  }\n\n  /**\n   * Update user preferences (placeholder for future implementation)\n   */\n  async updatePreferences(\n    id: number,\n    preferences: Record<string, any>\n  ): Promise<User> {\n    // This would require extending the user model to include preferences\n    // For now, we'll just return the current user\n    console.log('User preferences update not implemented:', preferences);\n    return this.getCurrentUser(id);\n  }\n\n  /**\n   * Get current user data\n   */\n  private async getCurrentUser(id: number): Promise<User> {\n    const response = await this.client.get<User>(ApiEndpoints.Users.ById(id));\n    \n    if (!response.success || !response.data) {\n      throw new Error('User not found');\n    }\n    \n    return response.data;\n  }\n\n  /**\n   * Validate update user request\n   */\n  private validateUpdateRequest(data: UpdateUserRequest): void {\n    if (!data.role || !['admin', 'customer', 'guest'].includes(data.role)) {\n      throw new Error('Valid role is required (admin, customer, or guest)');\n    }\n\n    // Validate phone number format if provided\n    if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {\n      throw new Error('Invalid phone number format');\n    }\n\n    // Validate name length if provided\n    if (data.name && data.name.length > 100) {\n      throw new Error('Name must be 100 characters or less');\n    }\n\n    // Validate address fields if provided\n    if (data.country && data.country.length > 100) {\n      throw new Error('Country must be 100 characters or less');\n    }\n\n    if (data.city && data.city.length > 100) {\n      throw new Error('City must be 100 characters or less');\n    }\n\n    if (data.zipCode && data.zipCode.length > 20) {\n      throw new Error('Zip code must be 20 characters or less');\n    }\n  }\n\n  /**\n   * Validate phone number format\n   */\n  private isValidPhoneNumber(phone: string): boolean {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n  }\n}\n\n// Export singleton instance\nexport const userUpdateService = new UserUpdateService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,0BAA0B,wIAAA,CAAA,cAAW;IAChD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAAuB,EAAiB;QAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;QAEpD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;IAEvD;IAEA;;GAEC,GACD,MAAM,SAAS,EAAU,EAAoB;QAC3C,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;QAErD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;IAElD;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAoB;QAC7C,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,CAAC;QAEvD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,CAAC;IAEpD;IAEA;;GAEC,GACD,MAAM,cACJ,EAAU,EACV,IAAa,EACb,WAAoB,EACpB,OAIC,EACc;QACf,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,OAA0B;YAC9B,MAAM,YAAY,IAAI;YACtB,MAAM,QAAQ,YAAY,IAAI;YAC9B,aAAa,eAAe,YAAY,WAAW;YACnD,SAAS,SAAS,WAAW,YAAY,OAAO;YAChD,MAAM,SAAS,QAAQ,YAAY,IAAI;YACvC,SAAS,SAAS,WAAW,YAAY,OAAO;YAChD,QAAQ,YAAY,MAAM;QAC5B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,cACJ,EAAU,EACV,WAAoB,EACpB,OAIC,EACc;QACf,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,OAA0B;YAC9B,MAAM,YAAY,IAAI;YACtB,MAAM,YAAY,IAAI;YACtB,aAAa,eAAe,YAAY,WAAW;YACnD,SAAS,SAAS,WAAW,YAAY,OAAO;YAChD,MAAM,SAAS,QAAQ,YAAY,IAAI;YACvC,SAAS,SAAS,WAAW,YAAY,OAAO;YAChD,QAAQ,YAAY,MAAM;QAC5B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAE,OAAe,EAAiB;QAC3D,IAAI,CAAC;YAAC;YAAS;YAAY;SAAQ,CAAC,QAAQ,CAAC,UAAU;YACrD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,OAA0B;YAC9B,MAAM;YACN,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW;YACpC,SAAS,YAAY,OAAO;YAC5B,MAAM,YAAY,IAAI;YACtB,SAAS,YAAY,OAAO;YAC5B,QAAQ,YAAY,MAAM;QAC5B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,cACJ,EAAU,EACV,OAAgB,EAChB,IAAa,EACb,OAAgB,EACD;QACf,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,OAA0B;YAC9B,MAAM,YAAY,IAAI;YACtB,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW;YACpC,SAAS,WAAW,YAAY,OAAO;YACvC,MAAM,QAAQ,YAAY,IAAI;YAC9B,SAAS,WAAW,YAAY,OAAO;YACvC,QAAQ,YAAY,MAAM;QAC5B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,mBAAmB,EAAU,EAAiB;QAClD,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,IAAI,YAAY,MAAM,EAAE;YACtB,MAAM,IAAI,CAAC,UAAU,CAAC;QACxB,OAAO;YACL,MAAM,IAAI,CAAC,QAAQ,CAAC;QACtB;QAEA,sBAAsB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA;;GAEC,GACD,MAAM,eAAe,OAAiB,EAAE,OAAe,EAIpD;QACD,IAAI,CAAC,UAAU,CAAC,OAAO,0BAA0B;YAC/C;YACA;YACA,OAAO,QAAQ,MAAM;QACvB;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,QAAQ,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,UAAU,CAAC,IAAI;QAGxC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,aAClB,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAG/F,OAAO;YACL,SAAS,eAAe,QAAQ,MAAM;YACtC,cAAc;YACd;QACF;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,OAAiB,EAIjC;QACD,IAAI,CAAC,UAAU,CAAC,SAAS,uBAAuB;YAC9C;YACA,OAAO,QAAQ,MAAM;QACvB;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,QAAQ,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,QAAQ,CAAC;QAGlC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,KAAK,MAClD,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAG/F,OAAO;YACL,SAAS,eAAe,QAAQ,MAAM;YACtC,gBAAgB;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,OAAiB,EAInC;QACD,IAAI,CAAC,UAAU,CAAC,SAAS,yBAAyB;YAChD;YACA,OAAO,QAAQ,MAAM;QACvB;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,QAAQ,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,UAAU,CAAC;QAGpC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,KAAK,MAClD,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAG/F,OAAO;YACL,SAAS,eAAe,QAAQ,MAAM;YACtC,kBAAkB;YAClB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAiB;QAC9C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAiB;QAChD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAM,kBACJ,EAAU,EACV,WAAgC,EACjB;QACf,qEAAqE;QACrE,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,4CAA4C;QACxD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA;;GAEC,GACD,MAAc,eAAe,EAAU,EAAiB;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC;QAErE,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAuB,EAAQ;QAC3D,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;YAAC;YAAS;YAAY;SAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrE,MAAM,IAAI,MAAM;QAClB;QAEA,2CAA2C;QAC3C,IAAI,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,WAAW,GAAG;YAClE,MAAM,IAAI,MAAM;QAClB;QAEA,mCAAmC;QACnC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK;YAC7C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI;YAC5C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAa,EAAW;QACjD,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;IACtD;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 2641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/delete.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\n\nexport class UserDeleteService extends BaseService {\n  /**\n   * Delete a user by ID\n   */\n  async delete(id: number): Promise<boolean> {\n    this.logApiCall('DELETE', ApiEndpoints.Users.ById(id));\n    \n    return this.handleVoidResponse(\n      this.client.delete(ApiEndpoints.Users.ById(id))\n    );\n  }\n\n  /**\n   * Delete multiple users\n   */\n  async deleteBatch(ids: number[]): Promise<boolean[]> {\n    this.logApiCall('DELETE', 'Batch Users', { count: ids.length });\n    \n    const promises = ids.map(id => this.delete(id));\n    return Promise.all(promises);\n  }\n\n  /**\n   * Soft delete - deactivate user instead of deleting\n   */\n  async softDelete(id: number): Promise<boolean> {\n    this.logApiCall('PATCH', `Soft Delete User ${id}`);\n    \n    try {\n      const { userUpdateService } = await import('./update');\n      await userUpdateService.deactivate(id);\n      return true;\n    } catch (error) {\n      console.error('Failed to soft delete user:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Check if user can be safely deleted\n   */\n  async canDelete(id: number): Promise<{\n    canDelete: boolean;\n    reason?: string;\n    hasOrders?: boolean;\n    isAdmin?: boolean;\n  }> {\n    try {\n      const { userGetService } = await import('./get');\n      const user = await userGetService.getById(id);\n\n      // Check if user is admin\n      const isAdmin = user.role === 'admin';\n      \n      // Check if user has orders (this would require order service integration)\n      // For now, we'll assume users can be deleted\n      const hasOrders = false; // Would check order history\n\n      // Admins typically shouldn't be deleted\n      const canDelete = !isAdmin && !hasOrders;\n      \n      let reason: string | undefined;\n      if (!canDelete) {\n        if (isAdmin) {\n          reason = 'Cannot delete admin users';\n        } else if (hasOrders) {\n          reason = 'Cannot delete users with order history';\n        }\n      }\n\n      return {\n        canDelete,\n        reason,\n        hasOrders,\n        isAdmin\n      };\n    } catch (error) {\n      console.error('Error checking if user can be deleted:', error);\n      return {\n        canDelete: false,\n        reason: 'Error checking user dependencies'\n      };\n    }\n  }\n\n  /**\n   * Safe delete - checks dependencies before deleting\n   */\n  async safeDelete(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const deleteCheck = await this.canDelete(id);\n      \n      if (!deleteCheck.canDelete) {\n        return {\n          success: false,\n          message: deleteCheck.reason || 'User cannot be deleted'\n        };\n      }\n\n      const deleted = await this.delete(id);\n      \n      if (deleted) {\n        return {\n          success: true,\n          message: 'User deleted successfully'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Failed to delete user'\n        };\n      }\n    } catch (error) {\n      console.error('Error during safe delete:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\n      };\n    }\n  }\n\n  /**\n   * Archive user (soft delete with archive flag)\n   */\n  async archive(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      // Deactivate the user\n      const { userUpdateService } = await import('./update');\n      await userUpdateService.deactivate(id);\n      \n      return {\n        success: true,\n        message: 'User archived successfully'\n      };\n    } catch (error) {\n      console.error('Error archiving user:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to archive user'\n      };\n    }\n  }\n\n  /**\n   * Restore archived user\n   */\n  async restore(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const { userUpdateService } = await import('./update');\n      await userUpdateService.activate(id);\n      \n      return {\n        success: true,\n        message: 'User restored successfully'\n      };\n    } catch (error) {\n      console.error('Error restoring user:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to restore user'\n      };\n    }\n  }\n\n  /**\n   * Delete inactive users (cleanup operation)\n   */\n  async deleteInactiveUsers(inactiveDays: number = 365): Promise<{\n    success: boolean;\n    message: string;\n    deletedCount: number;\n  }> {\n    try {\n      const { userGetService } = await import('./get');\n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - inactiveDays);\n\n      // Get inactive users\n      const inactiveUsers = await userGetService.getInactive();\n      \n      // Filter users inactive for specified period\n      const oldInactiveUsers = inactiveUsers.filter(user => \n        new Date(user.createdAt) < cutoffDate\n      );\n\n      if (oldInactiveUsers.length === 0) {\n        return {\n          success: true,\n          message: `No inactive users older than ${inactiveDays} days found`,\n          deletedCount: 0\n        };\n      }\n\n      // Check which users can be safely deleted\n      const deletableUsers: number[] = [];\n      for (const user of oldInactiveUsers) {\n        const canDelete = await this.canDelete(user.id);\n        if (canDelete.canDelete) {\n          deletableUsers.push(user.id);\n        }\n      }\n\n      if (deletableUsers.length === 0) {\n        return {\n          success: false,\n          message: 'No inactive users can be safely deleted',\n          deletedCount: 0\n        };\n      }\n\n      const results = await this.deleteBatch(deletableUsers);\n      const deletedCount = results.filter(result => result).length;\n      \n      return {\n        success: deletedCount === deletableUsers.length,\n        message: `Deleted ${deletedCount} inactive users`,\n        deletedCount\n      };\n    } catch (error) {\n      console.error('Error deleting inactive users:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to delete inactive users',\n        deletedCount: 0\n      };\n    }\n  }\n\n  /**\n   * Delete guest users (cleanup operation)\n   */\n  async deleteGuestUsers(): Promise<{\n    success: boolean;\n    message: string;\n    deletedCount: number;\n  }> {\n    try {\n      const { userGetService } = await import('./get');\n      const guestUsers = await userGetService.getByRole('guest');\n      \n      if (guestUsers.length === 0) {\n        return {\n          success: true,\n          message: 'No guest users found',\n          deletedCount: 0\n        };\n      }\n\n      const userIds = guestUsers.map(user => user.id);\n      const results = await this.deleteBatch(userIds);\n      const deletedCount = results.filter(result => result).length;\n      \n      return {\n        success: deletedCount === guestUsers.length,\n        message: `Deleted ${deletedCount} guest users`,\n        deletedCount\n      };\n    } catch (error) {\n      console.error('Error deleting guest users:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to delete guest users',\n        deletedCount: 0\n      };\n    }\n  }\n\n  /**\n   * Anonymize user data (GDPR compliance)\n   */\n  async anonymize(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const { userUpdateService } = await import('./update');\n      \n      // Replace personal data with anonymized values\n      const anonymizedData = {\n        role: 'customer',\n        name: 'Anonymized User',\n        phoneNumber: undefined,\n        country: undefined,\n        city: undefined,\n        zipCode: undefined,\n        active: false\n      };\n\n      await userUpdateService.update(id, anonymizedData);\n      \n      return {\n        success: true,\n        message: 'User data anonymized successfully'\n      };\n    } catch (error) {\n      console.error('Error anonymizing user:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to anonymize user'\n      };\n    }\n  }\n\n  /**\n   * Bulk soft delete (deactivate multiple users)\n   */\n  async bulkSoftDelete(ids: number[]): Promise<{\n    success: boolean;\n    deactivatedCount: number;\n    errors: string[];\n  }> {\n    this.logApiCall('PATCH', 'Bulk Soft Delete Users', { count: ids.length });\n\n    const results = await Promise.allSettled(\n      ids.map(id => this.softDelete(id))\n    );\n\n    const successful = results.filter(result => \n      result.status === 'fulfilled' && result.value === true\n    ).length;\n\n    const errors = results\n      .filter(result => result.status === 'rejected')\n      .map((result, index) => \n        `User ${ids[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\n      );\n\n    return {\n      success: successful === ids.length,\n      deactivatedCount: successful,\n      errors\n    };\n  }\n}\n\n// Export singleton instance\nexport const userDeleteService = new UserDeleteService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,0BAA0B,wIAAA,CAAA,cAAW;IAChD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC;QAElD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC;IAE/C;IAEA;;GAEC,GACD,MAAM,YAAY,GAAa,EAAsB;QACnD,IAAI,CAAC,UAAU,CAAC,UAAU,eAAe;YAAE,OAAO,IAAI,MAAM;QAAC;QAE7D,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,MAAM,CAAC;QAC3C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAoB;QAC7C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI;QAEjD,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,kBAAkB,UAAU,CAAC;YACnC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAKvB;QACD,IAAI;YACF,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,OAAO,MAAM,eAAe,OAAO,CAAC;YAE1C,yBAAyB;YACzB,MAAM,UAAU,KAAK,IAAI,KAAK;YAE9B,0EAA0E;YAC1E,6CAA6C;YAC7C,MAAM,YAAY,OAAO,4BAA4B;YAErD,wCAAwC;YACxC,MAAM,YAAY,CAAC,WAAW,CAAC;YAE/B,IAAI;YACJ,IAAI,CAAC,WAAW;gBACd,IAAI,SAAS;oBACX,SAAS;gBACX,OAAO,IAAI,WAAW;oBACpB,SAAS;gBACX;YACF;YAEA,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAGxB;QACD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC;YAEzC,IAAI,CAAC,YAAY,SAAS,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,YAAY,MAAM,IAAI;gBACjC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAElC,IAAI,SAAS;gBACX,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAGrB;QACD,IAAI;YACF,sBAAsB;YACtB,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,kBAAkB,UAAU,CAAC;YAEnC,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAGrB;QACD,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,kBAAkB,QAAQ,CAAC;YAEjC,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,oBAAoB,eAAuB,GAAG,EAIjD;QACD,IAAI;YACF,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,qBAAqB;YACrB,MAAM,gBAAgB,MAAM,eAAe,WAAW;YAEtD,6CAA6C;YAC7C,MAAM,mBAAmB,cAAc,MAAM,CAAC,CAAA,OAC5C,IAAI,KAAK,KAAK,SAAS,IAAI;YAG7B,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,6BAA6B,EAAE,aAAa,WAAW,CAAC;oBAClE,cAAc;gBAChB;YACF;YAEA,0CAA0C;YAC1C,MAAM,iBAA2B,EAAE;YACnC,KAAK,MAAM,QAAQ,iBAAkB;gBACnC,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;gBAC9C,IAAI,UAAU,SAAS,EAAE;oBACvB,eAAe,IAAI,CAAC,KAAK,EAAE;gBAC7B;YACF;YAEA,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,eAAe,MAAM;gBAC/C,SAAS,CAAC,QAAQ,EAAE,aAAa,eAAe,CAAC;gBACjD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBAIH;QACD,IAAI;YACF,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,aAAa,MAAM,eAAe,SAAS,CAAC;YAElD,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,MAAM,UAAU,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;YAC9C,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,WAAW,MAAM;gBAC3C,SAAS,CAAC,QAAQ,EAAE,aAAa,YAAY,CAAC;gBAC9C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAGvB;QACD,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAE9B,+CAA+C;YAC/C,MAAM,iBAAiB;gBACrB,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,QAAQ;YACV;YAEA,MAAM,kBAAkB,MAAM,CAAC,IAAI;YAEnC,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,GAAa,EAI/B;QACD,IAAI,CAAC,UAAU,CAAC,SAAS,0BAA0B;YAAE,OAAO,IAAI,MAAM;QAAC;QAEvE,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,UAAU,CAAC;QAGhC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,KAAK,MAClD,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAG3F,OAAO;YACL,SAAS,eAAe,IAAI,MAAM;YAClC,kBAAkB;YAClB;QACF;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 2911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/index.ts"], "sourcesContent": ["// User Services\nexport { UserGetService, userGetService } from './get';\nexport { UserPostService, userPostService } from './post';\nexport { UserUpdateService, userUpdateService } from './update';\nexport { UserDeleteService, userDeleteService } from './delete';\n\n// Combined User Service\nimport { userGetService } from './get';\nimport { userPostService } from './post';\nimport { userUpdateService } from './update';\nimport { userDeleteService } from './delete';\n\nexport class UserService {\n  get = userGetService;\n  post = userPostService;\n  update = userUpdateService;\n  delete = userDeleteService;\n}\n\n// Export singleton instance\nexport const userService = new UserService();\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAChB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,sIAAA,CAAA,iBAAc,CAAC;IACrB,OAAO,uIAAA,CAAA,kBAAe,CAAC;IACvB,SAAS,yIAAA,CAAA,oBAAiB,CAAC;IAC3B,SAAS,yIAAA,CAAA,oBAAiB,CAAC;AAC7B;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 2953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/seed/index.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\n\nexport class SeedService extends BaseService {\n  /**\n   * Seed all data (statuses, admin user, sample collections and products)\n   */\n  async seedAll(): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    this.logApiCall('POST', ApiEndpoints.Seed.All);\n    \n    try {\n      const response = await this.client.post(ApiEndpoints.Seed.All);\n      return {\n        success: response.success,\n        message: response.message || 'All data seeded successfully'\n      };\n    } catch (error) {\n      console.error('Error seeding all data:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to seed data'\n      };\n    }\n  }\n\n  /**\n   * Seed status data\n   */\n  async seedStatuses(): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    this.logApiCall('POST', ApiEndpoints.Seed.Statuses);\n    \n    try {\n      const response = await this.client.post(ApiEndpoints.Seed.Statuses);\n      return {\n        success: response.success,\n        message: response.message || 'Status data seeded successfully'\n      };\n    } catch (error) {\n      console.error('Error seeding statuses:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to seed statuses'\n      };\n    }\n  }\n\n  /**\n   * Seed admin user\n   */\n  async seedAdminUser(): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    this.logApiCall('POST', ApiEndpoints.Seed.AdminUser);\n    \n    try {\n      const response = await this.client.post(ApiEndpoints.Seed.AdminUser);\n      return {\n        success: response.success,\n        message: response.message || 'Admin user seeded successfully'\n      };\n    } catch (error) {\n      console.error('Error seeding admin user:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to seed admin user'\n      };\n    }\n  }\n\n  /**\n   * Seed sample collections\n   */\n  async seedCollections(): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    this.logApiCall('POST', ApiEndpoints.Seed.Collections);\n    \n    try {\n      const response = await this.client.post(ApiEndpoints.Seed.Collections);\n      return {\n        success: response.success,\n        message: response.message || 'Sample collections seeded successfully'\n      };\n    } catch (error) {\n      console.error('Error seeding collections:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to seed collections'\n      };\n    }\n  }\n\n  /**\n   * Seed sample products\n   */\n  async seedProducts(): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    this.logApiCall('POST', ApiEndpoints.Seed.Products);\n    \n    try {\n      const response = await this.client.post(ApiEndpoints.Seed.Products);\n      return {\n        success: response.success,\n        message: response.message || 'Sample products seeded successfully'\n      };\n    } catch (error) {\n      console.error('Error seeding products:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to seed products'\n      };\n    }\n  }\n\n  /**\n   * Initialize database with all required data\n   */\n  async initializeDatabase(): Promise<{\n    success: boolean;\n    message: string;\n    steps: Array<{\n      step: string;\n      success: boolean;\n      message: string;\n    }>;\n  }> {\n    const steps: Array<{\n      step: string;\n      success: boolean;\n      message: string;\n    }> = [];\n\n    try {\n      // Step 1: Seed statuses\n      const statusResult = await this.seedStatuses();\n      steps.push({\n        step: 'Seed Statuses',\n        success: statusResult.success,\n        message: statusResult.message\n      });\n\n      // Step 2: Seed admin user\n      const adminResult = await this.seedAdminUser();\n      steps.push({\n        step: 'Seed Admin User',\n        success: adminResult.success,\n        message: adminResult.message\n      });\n\n      // Step 3: Seed collections\n      const collectionsResult = await this.seedCollections();\n      steps.push({\n        step: 'Seed Collections',\n        success: collectionsResult.success,\n        message: collectionsResult.message\n      });\n\n      // Step 4: Seed products\n      const productsResult = await this.seedProducts();\n      steps.push({\n        step: 'Seed Products',\n        success: productsResult.success,\n        message: productsResult.message\n      });\n\n      const allSuccessful = steps.every(step => step.success);\n\n      return {\n        success: allSuccessful,\n        message: allSuccessful \n          ? 'Database initialized successfully' \n          : 'Database initialization completed with some errors',\n        steps\n      };\n    } catch (error) {\n      console.error('Error initializing database:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to initialize database',\n        steps\n      };\n    }\n  }\n\n  /**\n   * Check if database needs seeding\n   */\n  async checkSeedingStatus(): Promise<{\n    needsSeeding: boolean;\n    missingData: string[];\n  }> {\n    try {\n      // This would require additional endpoints to check data existence\n      // For now, we'll return a placeholder response\n      return {\n        needsSeeding: false,\n        missingData: []\n      };\n    } catch (error) {\n      console.error('Error checking seeding status:', error);\n      return {\n        needsSeeding: true,\n        missingData: ['Unable to check status']\n      };\n    }\n  }\n\n  /**\n   * Reset and reseed database (development only)\n   */\n  async resetAndReseed(): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    if (process.env.NODE_ENV === 'production') {\n      return {\n        success: false,\n        message: 'Reset and reseed is not allowed in production'\n      };\n    }\n\n    try {\n      // This would require additional endpoints to clear data\n      // For now, we'll just seed all data\n      return await this.seedAll();\n    } catch (error) {\n      console.error('Error resetting and reseeding:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to reset and reseed'\n      };\n    }\n  }\n}\n\n// Export singleton instance\nexport const seedService = new SeedService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,oBAAoB,wIAAA,CAAA,cAAW;IAC1C;;GAEC,GACD,MAAM,UAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG;QAE7C,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG;YAC7D,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;QAElD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;YAClE,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,gBAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;QAEnD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;YACnE,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,kBAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;QAErD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;YACrE,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;QAElD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;YAClE,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,qBAQH;QACD,MAAM,QAID,EAAE;QAEP,IAAI;YACF,wBAAwB;YACxB,MAAM,eAAe,MAAM,IAAI,CAAC,YAAY;YAC5C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,aAAa,OAAO;gBAC7B,SAAS,aAAa,OAAO;YAC/B;YAEA,0BAA0B;YAC1B,MAAM,cAAc,MAAM,IAAI,CAAC,aAAa;YAC5C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,YAAY,OAAO;gBAC5B,SAAS,YAAY,OAAO;YAC9B;YAEA,2BAA2B;YAC3B,MAAM,oBAAoB,MAAM,IAAI,CAAC,eAAe;YACpD,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,kBAAkB,OAAO;gBAClC,SAAS,kBAAkB,OAAO;YACpC;YAEA,wBAAwB;YACxB,MAAM,iBAAiB,MAAM,IAAI,CAAC,YAAY;YAC9C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,eAAe,OAAO;gBAC/B,SAAS,eAAe,OAAO;YACjC;YAEA,MAAM,gBAAgB,MAAM,KAAK,CAAC,CAAA,OAAQ,KAAK,OAAO;YAEtD,OAAO;gBACL,SAAS;gBACT,SAAS,gBACL,sCACA;gBACJ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,qBAGH;QACD,IAAI;YACF,kEAAkE;YAClE,+CAA+C;YAC/C,OAAO;gBACL,cAAc;gBACd,aAAa,EAAE;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBACL,cAAc;gBACd,aAAa;oBAAC;iBAAyB;YACzC;QACF;IACF;IAEA;;GAEC,GACD,MAAM,iBAGH;QACD,uCAA2C;;QAK3C;QAEA,IAAI;YACF,wDAAwD;YACxD,oCAAoC;YACpC,OAAO,MAAM,IAAI,CAAC,OAAO;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 3146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/index.ts"], "sourcesContent": ["// Configuration and Base Services\nexport * from './config/apiConfig';\nexport * from './config/httpClient';\nexport * from './config/baseService';\n\n// Type Definitions\nexport * from './types/entities';\n\n// Individual Service Exports\nexport * from './api/collections';\nexport * from './api/products';\nexport * from './api/orders';\nexport * from './api/users';\nexport * from './api/seed';\n\n// Combined API Service\nimport { collectionService } from './api/collections';\nimport { productService } from './api/products';\nimport { orderService } from './api/orders';\nimport { userService } from './api/users';\nimport { seedService } from './api/seed';\n\nexport class ApiService {\n  collections = collectionService;\n  products = productService;\n  orders = orderService;\n  users = userService;\n  seed = seedService;\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\n\n// Default export for convenience\nexport default apiService;\n\n// Re-export commonly used services for direct access\nexport {\n  collectionService,\n  productService,\n  orderService,\n  userService,\n  seedService\n};\n\n// Utility exports\nexport { ServiceUtils } from './config/baseService';\nexport { buildQueryString, ApiError } from './config/apiConfig';\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;;AAClC;AACA;AACA;AAEA,mBAAmB;AACnB;AAEA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AAEA,uBAAuB;AACvB;AACA;AACA;AACA;;;;;;;;;;;;;;;AAGO,MAAM;IACX,cAAc,8JAAA,CAAA,oBAAiB,CAAC;IAChC,WAAW,2JAAA,CAAA,iBAAc,CAAC;IAC1B,SAAS,yJAAA,CAAA,eAAY,CAAC;IACtB,QAAQ,wJAAA,CAAA,cAAW,CAAC;IACpB,OAAO,uIAAA,CAAA,cAAW,CAAC;AACrB;AAGO,MAAM,aAAa,IAAI;uCAGf", "debugId": null}}, {"offset": {"line": 3217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/admin/ProductModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { productService } from '@/services';\nimport { Product, Collection, CreateProductRequest, UpdateProductRequest } from '@/services/types/entities';\n\ninterface ProductModalProps {\n  product?: Product | null;\n  collections: Collection[];\n  onClose: () => void;\n  onSuccess: () => void;\n}\n\nexport default function ProductModal({ product, collections, onClose, onSuccess }: ProductModalProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  \n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: 0,\n    stock: 0,\n    collectionId: 0,\n    images: [] as string[],\n    tags: [] as string[],\n  });\n\n  const [tagInput, setTagInput] = useState('');\n  const [imageInput, setImageInput] = useState('');\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        description: product.description || '',\n        price: product.price,\n        stock: product.stock,\n        collectionId: product.collectionId,\n        images: product.images || [],\n        tags: product.tags || [],\n      });\n    }\n  }, [product]);\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length > 200) {\n      newErrors.name = 'Name must be less than 200 characters';\n    }\n\n    if (formData.description && formData.description.length > 1000) {\n      newErrors.description = 'Description must be less than 1000 characters';\n    }\n\n    if (formData.price <= 0) {\n      newErrors.price = 'Price must be greater than 0';\n    }\n\n    if (formData.stock < 0) {\n      newErrors.stock = 'Stock cannot be negative';\n    }\n\n    if (!formData.collectionId) {\n      newErrors.collectionId = 'Collection is required';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    \n    try {\n      if (product) {\n        // Update existing product\n        const updateData: UpdateProductRequest = formData;\n        await productService.update.updateById(product.id, updateData);\n      } else {\n        // Create new product\n        const createData: CreateProductRequest = formData;\n        await productService.post.create(createData);\n      }\n      \n      onSuccess();\n    } catch (error) {\n      console.error('Error saving product:', error);\n      setErrors({ submit: 'Failed to save product. Please try again.' });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleAddTag = () => {\n    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, tagInput.trim()]\n      }));\n      setTagInput('');\n    }\n  };\n\n  const handleRemoveTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  const handleAddImage = () => {\n    if (imageInput.trim() && !formData.images.includes(imageInput.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        images: [...prev.images, imageInput.trim()]\n      }));\n      setImageInput('');\n    }\n  };\n\n  const handleRemoveImage = (imageToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter(image => image !== imageToRemove)\n    }));\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            {product ? 'Edit Product' : 'Add New Product'}\n          </h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Name */}\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Name *\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              value={formData.name}\n              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${\n                errors.name ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"Enter product name\"\n            />\n            {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\n          </div>\n\n          {/* Description */}\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Description\n            </label>\n            <textarea\n              id=\"description\"\n              value={formData.description}\n              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              rows={3}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${\n                errors.description ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"Enter product description\"\n            />\n            {errors.description && <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>}\n          </div>\n\n          {/* Price and Stock */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Price *\n              </label>\n              <input\n                type=\"number\"\n                id=\"price\"\n                step=\"0.01\"\n                min=\"0\"\n                value={formData.price}\n                onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${\n                  errors.price ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0.00\"\n              />\n              {errors.price && <p className=\"mt-1 text-sm text-red-600\">{errors.price}</p>}\n            </div>\n\n            <div>\n              <label htmlFor=\"stock\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Stock *\n              </label>\n              <input\n                type=\"number\"\n                id=\"stock\"\n                min=\"0\"\n                value={formData.stock}\n                onChange={(e) => setFormData(prev => ({ ...prev, stock: parseInt(e.target.value) || 0 }))}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${\n                  errors.stock ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0\"\n              />\n              {errors.stock && <p className=\"mt-1 text-sm text-red-600\">{errors.stock}</p>}\n            </div>\n          </div>\n\n          {/* Collection */}\n          <div>\n            <label htmlFor=\"collection\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Collection *\n            </label>\n            <select\n              id=\"collection\"\n              value={formData.collectionId}\n              onChange={(e) => setFormData(prev => ({ ...prev, collectionId: parseInt(e.target.value) || 0 }))}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${\n                errors.collectionId ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value={0}>Select a collection</option>\n              {collections.map(collection => (\n                <option key={collection.id} value={collection.id}>\n                  {collection.name} (Level {collection.level})\n                </option>\n              ))}\n            </select>\n            {errors.collectionId && <p className=\"mt-1 text-sm text-red-600\">{errors.collectionId}</p>}\n          </div>\n\n          {/* Images */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Images\n            </label>\n            <div className=\"space-y-2 mb-2\">\n              {formData.images.map((image, index) => (\n                <div key={index} className=\"flex items-center justify-between bg-gray-50 p-2 rounded\">\n                  <span className=\"text-sm text-gray-700 truncate flex-1\">{image}</span>\n                  <button\n                    type=\"button\"\n                    onClick={() => handleRemoveImage(image)}\n                    className=\"ml-2 text-red-600 hover:text-red-800\"\n                  >\n                    ×\n                  </button>\n                </div>\n              ))}\n            </div>\n            <div className=\"flex\">\n              <input\n                type=\"url\"\n                value={imageInput}\n                onChange={(e) => setImageInput(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddImage())}\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-amber-500 focus:border-amber-500\"\n                placeholder=\"Add image URL\"\n              />\n              <button\n                type=\"button\"\n                onClick={handleAddImage}\n                className=\"px-4 py-2 bg-amber-900 text-white rounded-r-md hover:bg-amber-800\"\n              >\n                Add\n              </button>\n            </div>\n          </div>\n\n          {/* Tags */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Tags\n            </label>\n            <div className=\"flex flex-wrap gap-2 mb-2\">\n              {formData.tags.map((tag, index) => (\n                <span\n                  key={index}\n                  className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\"\n                >\n                  {tag}\n                  <button\n                    type=\"button\"\n                    onClick={() => handleRemoveTag(tag)}\n                    className=\"ml-1 text-amber-600 hover:text-amber-800\"\n                  >\n                    ×\n                  </button>\n                </span>\n              ))}\n            </div>\n            <div className=\"flex\">\n              <input\n                type=\"text\"\n                value={tagInput}\n                onChange={(e) => setTagInput(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-amber-500 focus:border-amber-500\"\n                placeholder=\"Add a tag\"\n              />\n              <button\n                type=\"button\"\n                onClick={handleAddTag}\n                className=\"px-4 py-2 bg-amber-900 text-white rounded-r-md hover:bg-amber-800\"\n              >\n                Add\n              </button>\n            </div>\n          </div>\n\n          {/* Submit Error */}\n          {errors.submit && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n              {errors.submit}\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? 'Saving...' : (product ? 'Update' : 'Create')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAae,SAAS,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAqB;IAClG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,cAAc;QACd,QAAQ,EAAE;QACV,MAAM,EAAE;IACV;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,YAAY;gBACV,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW,IAAI;gBACpC,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,KAAK;gBACpB,cAAc,QAAQ,YAAY;gBAClC,QAAQ,QAAQ,MAAM,IAAI,EAAE;gBAC5B,MAAM,QAAQ,IAAI,IAAI,EAAE;YAC1B;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,KAAK;YACrC,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,MAAM;YAC9D,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,SAAS,KAAK,IAAI,GAAG;YACvB,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,KAAK,GAAG,GAAG;YACtB,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,UAAU,YAAY,GAAG;QAC3B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,IAAI,SAAS;gBACX,0BAA0B;gBAC1B,MAAM,aAAmC;gBACzC,MAAM,2JAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE;YACrD,OAAO;gBACL,qBAAqB;gBACrB,MAAM,aAAmC;gBACzC,MAAM,2JAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YACnC;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,UAAU;gBAAE,QAAQ;YAA4C;QAClE,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,SAAS,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK;YAC/D,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,SAAS,IAAI;qBAAG;gBACvC,CAAC;YACD,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,IAAI,WAAW,IAAI,MAAM,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,WAAW,IAAI,KAAK;YACrE,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,QAAQ;2BAAI,KAAK,MAAM;wBAAE,WAAW,IAAI;qBAAG;gBAC7C,CAAC;YACD,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU;YAChD,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,UAAU,iBAAiB;;;;;;sCAE9B,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA+C;;;;;;8CAG/E,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAW,CAAC,kGAAkG,EAC5G,OAAO,IAAI,GAAG,mBAAmB,mBACjC;oCACF,aAAY;;;;;;gCAEb,OAAO,IAAI,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,IAAI;;;;;;;;;;;;sCAIvE,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,WAAW;oCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAC9E,MAAM;oCACN,WAAW,CAAC,kGAAkG,EAC5G,OAAO,WAAW,GAAG,mBAAmB,mBACxC;oCACF,aAAY;;;;;;gCAEb,OAAO,WAAW,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,WAAW;;;;;;;;;;;;sCAIrF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,KAAI;4CACJ,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDAAE,CAAC;4CACzF,WAAW,CAAC,kGAAkG,EAC5G,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4CACF,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAGzE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,KAAI;4CACJ,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oDAAE,CAAC;4CACvF,WAAW,CAAC,kGAAkG,EAC5G,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4CACF,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;sCAK3E,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAa,WAAU;8CAA+C;;;;;;8CAGrF,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAAE,CAAC;oCAC9F,WAAW,CAAC,kGAAkG,EAC5G,OAAO,YAAY,GAAG,mBAAmB,mBACzC;;sDAEF,8OAAC;4CAAO,OAAO;sDAAG;;;;;;wCACjB,YAAY,GAAG,CAAC,CAAA,2BACf,8OAAC;gDAA2B,OAAO,WAAW,EAAE;;oDAC7C,WAAW,IAAI;oDAAC;oDAAS,WAAW,KAAK;oDAAC;;+CADhC,WAAW,EAAE;;;;;;;;;;;gCAK7B,OAAO,YAAY,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,YAAY;;;;;;;;;;;;sCAIvF,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAK,WAAU;8DAAyC;;;;;;8DACzD,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DACX;;;;;;;2CANO;;;;;;;;;;8CAYd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,gBAAgB;4CAC7E,WAAU;4CACV,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACvB,8OAAC;4CAEC,WAAU;;gDAET;8DACD,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DACX;;;;;;;2CARI;;;;;;;;;;8CAcX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,cAAc;4CAC3E,WAAU;4CACV,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;wBAOJ,OAAO,MAAM,kBACZ,8OAAC;4BAAI,WAAU;sCACZ,OAAO,MAAM;;;;;;sCAKlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,eAAe,cAAe,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 3844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/admin/StockUpdateModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { productService } from '@/services';\nimport { Product } from '@/services/types/entities';\n\ninterface StockUpdateModalProps {\n  product: Product;\n  onClose: () => void;\n  onSuccess: () => void;\n}\n\nexport default function StockUpdateModal({ product, onClose, onSuccess }: StockUpdateModalProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState('');\n  const [updateType, setUpdateType] = useState<'set' | 'increase' | 'decrease'>('set');\n  const [amount, setAmount] = useState(0);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    let newStock: number;\n    \n    switch (updateType) {\n      case 'set':\n        newStock = amount;\n        break;\n      case 'increase':\n        newStock = product.stock + amount;\n        break;\n      case 'decrease':\n        newStock = product.stock - amount;\n        break;\n      default:\n        newStock = product.stock;\n    }\n\n    if (newStock < 0) {\n      setError('Stock cannot be negative');\n      return;\n    }\n\n    setIsSubmitting(true);\n    \n    try {\n      await productService.update.updateStock(product.id, newStock);\n      onSuccess();\n    } catch (error) {\n      console.error('Error updating stock:', error);\n      setError('Failed to update stock. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const getNewStockValue = () => {\n    switch (updateType) {\n      case 'set':\n        return amount;\n      case 'increase':\n        return product.stock + amount;\n      case 'decrease':\n        return Math.max(0, product.stock - amount);\n      default:\n        return product.stock;\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            Update Stock\n          </h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <div className=\"mb-4 p-3 bg-gray-50 rounded-md\">\n          <h4 className=\"font-medium text-gray-900\">{product.name}</h4>\n          <p className=\"text-sm text-gray-600\">Current Stock: <span className=\"font-medium\">{product.stock}</span></p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Update Type */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Update Type\n            </label>\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  value=\"set\"\n                  checked={updateType === 'set'}\n                  onChange={(e) => setUpdateType(e.target.value as any)}\n                  className=\"h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300\"\n                />\n                <span className=\"ml-2 text-sm text-gray-900\">Set to specific amount</span>\n              </label>\n              \n              <label className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  value=\"increase\"\n                  checked={updateType === 'increase'}\n                  onChange={(e) => setUpdateType(e.target.value as any)}\n                  className=\"h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300\"\n                />\n                <span className=\"ml-2 text-sm text-gray-900\">Increase by amount</span>\n              </label>\n              \n              <label className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  value=\"decrease\"\n                  checked={updateType === 'decrease'}\n                  onChange={(e) => setUpdateType(e.target.value as any)}\n                  className=\"h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300\"\n                />\n                <span className=\"ml-2 text-sm text-gray-900\">Decrease by amount</span>\n              </label>\n            </div>\n          </div>\n\n          {/* Amount */}\n          <div>\n            <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              {updateType === 'set' ? 'New Stock Amount' : 'Amount to ' + updateType}\n            </label>\n            <input\n              type=\"number\"\n              id=\"amount\"\n              min=\"0\"\n              value={amount}\n              onChange={(e) => setAmount(parseInt(e.target.value) || 0)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500\"\n              placeholder=\"Enter amount\"\n              required\n            />\n          </div>\n\n          {/* Preview */}\n          <div className=\"p-3 bg-blue-50 rounded-md\">\n            <p className=\"text-sm text-blue-800\">\n              <span className=\"font-medium\">New stock will be: </span>\n              <span className=\"font-bold\">{getNewStockValue()}</span>\n            </p>\n          </div>\n\n          {/* Error */}\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n              {error}\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? 'Updating...' : 'Update Stock'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAYe,SAAS,iBAAiB,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAyB;IAC7F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI;QAEJ,OAAQ;YACN,KAAK;g<PERSON><PERSON>,WAAW;gBACX;YACF,KAAK;gBACH,WAAW,QAAQ,KAAK,GAAG;gBAC3B;YACF,KAAK;gBACH,WAAW,QAAQ,KAAK,GAAG;gBAC3B;YACF;gBACE,WAAW,QAAQ,KAAK;QAC5B;QAEA,IAAI,WAAW,GAAG;YAChB,SAAS;YACT;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,2JAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,QAAQ,KAAK,GAAG;YACzB,KAAK;gBACH,OAAO,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,GAAG;YACrC;gBACE,OAAO,QAAQ,KAAK;QACxB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B,QAAQ,IAAI;;;;;;sCACvD,8OAAC;4BAAE,WAAU;;gCAAwB;8CAAe,8OAAC;oCAAK,WAAU;8CAAe,QAAQ,KAAK;;;;;;;;;;;;;;;;;;8BAGlG,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,OAAM;oDACN,SAAS,eAAe;oDACxB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,OAAM;oDACN,SAAS,eAAe;oDACxB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAG/C,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,OAAM;oDACN,SAAS,eAAe;oDACxB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAMnD,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAS,WAAU;8CAC/B,eAAe,QAAQ,qBAAqB,eAAe;;;;;;8CAE9D,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oCACvD,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,8OAAC;wCAAK,WAAU;kDAAa;;;;;;;;;;;;;;;;;wBAKhC,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,eAAe,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C", "debugId": null}}, {"offset": {"line": 4226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/admin/dashboard/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport ProtectedRoute from '@/components/admin/ProtectedRoute';\nimport AdminLayout from '@/components/admin/AdminLayout';\nimport ProductModal from '@/components/admin/ProductModal';\nimport StockUpdateModal from '@/components/admin/StockUpdateModal';\nimport { productService, collectionService } from '@/services';\nimport { Product, Collection } from '@/services/types/entities';\n\nexport default function ProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [collections, setCollections] = useState<Collection[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [isStockModalOpen, setIsStockModalOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [stockUpdateProduct, setStockUpdateProduct] = useState<Product | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [collectionFilter, setCollectionFilter] = useState<number | ''>('');\n  const [stockFilter, setStockFilter] = useState<'all' | 'inStock' | 'lowStock' | 'outOfStock'>('all');\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setIsLoading(true);\n      const [productsData, collectionsData] = await Promise.all([\n        productService.get.getAll(),\n        collectionService.get.getAll(),\n      ]);\n      setProducts(productsData);\n      setCollections(collectionsData);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setIsModalOpen(true);\n  };\n\n  const handleEditProduct = (product: Product) => {\n    setEditingProduct(product);\n    setIsModalOpen(true);\n  };\n\n  const handleUpdateStock = (product: Product) => {\n    setStockUpdateProduct(product);\n    setIsStockModalOpen(true);\n  };\n\n  const handleDeleteProduct = async (id: number) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await productService.delete.deleteById(id);\n        await fetchData();\n      } catch (error) {\n        console.error('Error deleting product:', error);\n        alert('Error deleting product. Please try again.');\n      }\n    }\n  };\n\n  const handleModalClose = () => {\n    setIsModalOpen(false);\n    setEditingProduct(null);\n  };\n\n  const handleStockModalClose = () => {\n    setIsStockModalOpen(false);\n    setStockUpdateProduct(null);\n  };\n\n  const handleModalSuccess = () => {\n    setIsModalOpen(false);\n    setEditingProduct(null);\n    fetchData();\n  };\n\n  const handleStockUpdateSuccess = () => {\n    setIsStockModalOpen(false);\n    setStockUpdateProduct(null);\n    fetchData();\n  };\n\n  const getCollectionName = (collectionId: number) => {\n    const collection = collections.find(c => c.id === collectionId);\n    return collection?.name || 'Unknown';\n  };\n\n  const getStockStatus = (stock: number) => {\n    if (stock === 0) return { label: 'Out of Stock', color: 'bg-red-100 text-red-800' };\n    if (stock < 10) return { label: 'Low Stock', color: 'bg-yellow-100 text-yellow-800' };\n    return { label: 'In Stock', color: 'bg-green-100 text-green-800' };\n  };\n\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         (product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);\n    \n    const matchesCollection = collectionFilter === '' || product.collectionId === collectionFilter;\n    \n    let matchesStock = true;\n    switch (stockFilter) {\n      case 'inStock':\n        matchesStock = product.stock > 10;\n        break;\n      case 'lowStock':\n        matchesStock = product.stock > 0 && product.stock <= 10;\n        break;\n      case 'outOfStock':\n        matchesStock = product.stock === 0;\n        break;\n    }\n    \n    return matchesSearch && matchesCollection && matchesStock;\n  });\n\n  return (\n    <ProtectedRoute>\n      <AdminLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Products Management</h1>\n              <p className=\"text-gray-600\">Manage your product catalog and inventory</p>\n            </div>\n            <button\n              onClick={handleAddProduct}\n              className=\"px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800 transition-colors flex items-center\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n              </svg>\n              Add Product\n            </button>\n          </div>\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Search Products\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"search\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  placeholder=\"Search by name or description...\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500\"\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"collection\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Filter by Collection\n                </label>\n                <select\n                  id=\"collection\"\n                  value={collectionFilter}\n                  onChange={(e) => setCollectionFilter(e.target.value === '' ? '' : Number(e.target.value))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500\"\n                >\n                  <option value=\"\">All Collections</option>\n                  {collections.map(collection => (\n                    <option key={collection.id} value={collection.id}>\n                      {collection.name} (Level {collection.level})\n                    </option>\n                  ))}\n                </select>\n              </div>\n              \n              <div>\n                <label htmlFor=\"stock\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Filter by Stock\n                </label>\n                <select\n                  id=\"stock\"\n                  value={stockFilter}\n                  onChange={(e) => setStockFilter(e.target.value as any)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500\"\n                >\n                  <option value=\"all\">All Products</option>\n                  <option value=\"inStock\">In Stock (>10)</option>\n                  <option value=\"lowStock\">Low Stock (1-10)</option>\n                  <option value=\"outOfStock\">Out of Stock (0)</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Products Table */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n            {isLoading ? (\n              <div className=\"p-8 text-center\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto\"></div>\n                <p className=\"mt-4 text-gray-600\">Loading products...</p>\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Product\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Collection\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Price\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Stock\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Created\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {filteredProducts.map((product) => {\n                      const stockStatus = getStockStatus(product.stock);\n                      return (\n                        <tr key={product.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <div>\n                              <div className=\"text-sm font-medium text-gray-900\">{product.name}</div>\n                              {product.description && (\n                                <div className=\"text-sm text-gray-500 truncate max-w-xs\">\n                                  {product.description}\n                                </div>\n                              )}\n                            </div>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {getCollectionName(product.collectionId)}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            ${product.price.toFixed(2)}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <div className=\"flex items-center\">\n                              <span className=\"text-sm text-gray-900 mr-2\">{product.stock}</span>\n                              <button\n                                onClick={() => handleUpdateStock(product)}\n                                className=\"text-amber-600 hover:text-amber-900 text-xs\"\n                                title=\"Update stock\"\n                              >\n                                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                                </svg>\n                              </button>\n                            </div>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>\n                              {stockStatus.label}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {new Date(product.createdAt).toLocaleDateString()}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                            <button\n                              onClick={() => handleEditProduct(product)}\n                              className=\"text-amber-600 hover:text-amber-900 mr-3\"\n                            >\n                              Edit\n                            </button>\n                            <button\n                              onClick={() => handleDeleteProduct(product.id)}\n                              className=\"text-red-600 hover:text-red-900\"\n                            >\n                              Delete\n                            </button>\n                          </td>\n                        </tr>\n                      );\n                    })}\n                  </tbody>\n                </table>\n                \n                {filteredProducts.length === 0 && (\n                  <div className=\"p-8 text-center text-gray-500\">\n                    No products found matching your criteria.\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Product Modal */}\n        {isModalOpen && (\n          <ProductModal\n            product={editingProduct}\n            collections={collections}\n            onClose={handleModalClose}\n            onSuccess={handleModalSuccess}\n          />\n        )}\n\n        {/* Stock Update Modal */}\n        {isStockModalOpen && stockUpdateProduct && (\n          <StockUpdateModal\n            product={stockUpdateProduct}\n            onClose={handleStockModalClose}\n            onSuccess={handleStockUpdateSuccess}\n          />\n        )}\n      </AdminLayout>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiD;IAE9F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACxD,2JAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,MAAM;gBACzB,8JAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,MAAM;aAC7B;YACD,YAAY;YACZ,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,OAAO,OAAO,CAAC,kDAAkD;YACnE,IAAI;gBACF,MAAM,2JAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,UAAU,CAAC;gBACvC,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,MAAM;YACR;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,wBAAwB;QAC5B,oBAAoB;QACpB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB;QACzB,eAAe;QACf,kBAAkB;QAClB;IACF;IAEA,MAAM,2BAA2B;QAC/B,oBAAoB;QACpB,sBAAsB;QACtB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,OAAO,YAAY,QAAQ;IAC7B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;YAAE,OAAO;YAAgB,OAAO;QAA0B;QAClF,IAAI,QAAQ,IAAI,OAAO;YAAE,OAAO;YAAa,OAAO;QAAgC;QACpF,OAAO;YAAE,OAAO;YAAY,OAAO;QAA8B;IACnE;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,CAAC,QAAQ,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW,OAAO,KAAK;QAEpG,MAAM,oBAAoB,qBAAqB,MAAM,QAAQ,YAAY,KAAK;QAE9E,IAAI,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,eAAe,QAAQ,KAAK,GAAG;gBAC/B;YACF,KAAK;gBACH,eAAe,QAAQ,KAAK,GAAG,KAAK,QAAQ,KAAK,IAAI;gBACrD;YACF,KAAK;gBACH,eAAe,QAAQ,KAAK,KAAK;gBACjC;QACJ;QAEA,OAAO,iBAAiB,qBAAqB;IAC/C;IAEA,qBACE,8OAAC,6IAAA,CAAA,UAAc;kBACb,cAAA,8OAAC,0IAAA,CAAA,UAAW;;8BACV,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;sCAMV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAS,WAAU;0DAA+C;;;;;;0DAGjF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAa,WAAU;0DAA+C;;;;;;0DAGrF,8OAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK;gDACvF,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,YAAY,GAAG,CAAC,CAAA,2BACf,8OAAC;4DAA2B,OAAO,WAAW,EAAE;;gEAC7C,WAAW,IAAI;gEAAC;gEAAS,WAAW,KAAK;gEAAC;;2DADhC,WAAW,EAAE;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,8OAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOnC,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;qDAGpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,WAAU;0DACf,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,8OAAC;4DAAG,WAAU;sEAAkF;;;;;;;;;;;;;;;;;0DAKpG,8OAAC;gDAAM,WAAU;0DACd,iBAAiB,GAAG,CAAC,CAAC;oDACrB,MAAM,cAAc,eAAe,QAAQ,KAAK;oDAChD,qBACE,8OAAC;wDAAoB,WAAU;;0EAC7B,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAqC,QAAQ,IAAI;;;;;;wEAC/D,QAAQ,WAAW,kBAClB,8OAAC;4EAAI,WAAU;sFACZ,QAAQ,WAAW;;;;;;;;;;;;;;;;;0EAK5B,8OAAC;gEAAG,WAAU;0EACX,kBAAkB,QAAQ,YAAY;;;;;;0EAEzC,8OAAC;gEAAG,WAAU;;oEAAoD;oEAC9D,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;0EAE1B,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA8B,QAAQ,KAAK;;;;;;sFAC3D,8OAAC;4EACC,SAAS,IAAM,kBAAkB;4EACjC,WAAU;4EACV,OAAM;sFAEN,cAAA,8OAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACjE,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAK7E,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAK,WAAW,CAAC,yDAAyD,EAAE,YAAY,KAAK,EAAE;8EAC7F,YAAY,KAAK;;;;;;;;;;;0EAGtB,8OAAC;gEAAG,WAAU;0EACX,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;0EAEjD,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEACC,SAAS,IAAM,kBAAkB;wEACjC,WAAU;kFACX;;;;;;kFAGD,8OAAC;wEACC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;wEAC7C,WAAU;kFACX;;;;;;;;;;;;;uDAjDI,QAAQ,EAAE;;;;;gDAuDvB;;;;;;;;;;;;oCAIH,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;gBAUxD,6BACC,8OAAC,2IAAA,CAAA,UAAY;oBACX,SAAS;oBACT,aAAa;oBACb,SAAS;oBACT,WAAW;;;;;;gBAKd,oBAAoB,oCACnB,8OAAC,+IAAA,CAAA,UAAgB;oBACf,SAAS;oBACT,SAAS;oBACT,WAAW;;;;;;;;;;;;;;;;;AAMvB", "debugId": null}}]}