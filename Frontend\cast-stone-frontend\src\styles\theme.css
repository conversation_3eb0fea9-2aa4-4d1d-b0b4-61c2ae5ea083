/* Cast Stone Website Theme - Navy Blue & White */
:root {
  /* Primary Navy Blue Colors */
  --cast-stone-navy: #1e3a8a;           /* Primary navy blue (was #4a3728 brown) */
  --cast-stone-light-navy: #3b82f6;     /* Light navy blue (was #6b4e3d light brown) */
  --cast-stone-dark-navy: #1e40af;      /* Dark navy blue for accents */
  --cast-stone-navy-gray: #64748b;      /* Navy-gray (was #8b7355 brown-gray) */
  
  /* Background Colors */
  --cast-stone-cream: #f8fafc;          /* Light cream/off-white background */
  --cast-stone-white: #ffffff;          /* Pure white */
  
  /* Accent Colors */
  --cast-stone-gold: #f59e0b;           /* Gold accent for highlights */
  --cast-stone-light-gold: #fbbf24;     /* Light gold */
  
  /* Shadow Colors */
  --cast-stone-shadow: rgba(30, 58, 138, 0.1);        /* Navy shadow */
  --cast-stone-shadow-hover: rgba(30, 58, 138, 0.15); /* Navy shadow hover */
  
  /* Transition Effects */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --cast-stone-navy: #3b82f6;
    --cast-stone-light-navy: #60a5fa;
    --cast-stone-cream: #0f172a;
    --cast-stone-white: #1e293b;
  }
}
