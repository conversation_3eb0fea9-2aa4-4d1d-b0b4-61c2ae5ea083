{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/cart/cart.module.css"], "sourcesContent": ["/* Cart Page Styles - Magazine/Editorial Theme */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n  min-height: 80vh;\n}\n\n/* Header */\n.header {\n  text-align: center;\n  margin-bottom: 3rem;\n  padding-bottom: 2rem;\n  border-bottom: 2px solid #f3f4f6;\n}\n\n.title {\n  color: #4a3728;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  line-height: 1.2;\n}\n\n.subtitle {\n  color: #6b5b4d;\n  font-size: 1.1rem;\n  margin: 0;\n}\n\n/* Loading State */\n.loadingContainer {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  color: #6b5b4d;\n}\n\n.loadingSpinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid #f3f4f6;\n  border-top: 3px solid #8b7355;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Empty Cart */\n.emptyCart {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  min-height: 400px;\n  padding: 2rem;\n}\n\n.emptyIcon {\n  width: 120px;\n  height: 120px;\n  color: #d1d5db;\n  margin-bottom: 2rem;\n}\n\n.emptyIcon svg {\n  width: 100%;\n  height: 100%;\n  stroke-width: 1.5;\n}\n\n.emptyTitle {\n  color: #4a3728;\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 0 0 1rem 0;\n}\n\n.emptyMessage {\n  color: #6b5b4d;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  margin: 0 0 2rem 0;\n  max-width: 500px;\n}\n\n.shopNowBtn {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  background: #8b7355;\n  color: white;\n  border-radius: 6px;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 1.1rem;\n  transition: all 0.3s ease;\n}\n\n.shopNowBtn:hover {\n  background: #6d5a47;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);\n}\n\n.shopIcon {\n  width: 20px;\n  height: 20px;\n  stroke-width: 2;\n}\n\n/* Error Message */\n.errorMessage {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 6px;\n  padding: 1rem;\n  margin-bottom: 2rem;\n  color: #dc2626;\n}\n\n.errorIcon {\n  width: 20px;\n  height: 20px;\n  flex-shrink: 0;\n  stroke-width: 2;\n}\n\n/* Cart Content */\n.cartContent {\n  display: grid;\n  grid-template-columns: 1fr 350px;\n  gap: 3rem;\n  margin-bottom: 3rem;\n}\n\n/* Cart Items */\n.cartItems {\n  min-width: 0; /* Allow content to shrink */\n}\n\n.itemsHeader {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.itemsHeader h2 {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0;\n}\n\n.continueShoppingLink {\n  color: #8b7355;\n  text-decoration: none;\n  font-weight: 600;\n  transition: color 0.2s ease;\n}\n\n.continueShoppingLink:hover {\n  color: #6d5a47;\n  text-decoration: underline;\n}\n\n.itemsList {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n/* Cart Summary Container */\n.cartSummaryContainer {\n  /* Styles handled by CartSummary component */\n}\n\n/* Additional Actions */\n.additionalActions {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  padding-top: 2rem;\n  border-top: 2px solid #f3f4f6;\n}\n\n.helpSection,\n.shippingInfo {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.helpSection h3,\n.shippingInfo h3 {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0 0 1rem 0;\n}\n\n.helpSection p {\n  color: #6b5b4d;\n  line-height: 1.6;\n  margin: 0;\n}\n\n.contactLink {\n  color: #8b7355;\n  text-decoration: none;\n  font-weight: 600;\n}\n\n.contactLink:hover {\n  text-decoration: underline;\n}\n\n.shippingInfo ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.shippingInfo li {\n  color: #6b5b4d;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e5e7eb;\n  position: relative;\n  padding-left: 1.5rem;\n}\n\n.shippingInfo li:last-child {\n  border-bottom: none;\n}\n\n.shippingInfo li::before {\n  content: '✓';\n  position: absolute;\n  left: 0;\n  color: #059669;\n  font-weight: bold;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .cartContent {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .container {\n    padding: 1rem;\n  }\n\n  .header {\n    margin-bottom: 2rem;\n    padding-bottom: 1.5rem;\n  }\n\n  .title {\n    font-size: 2rem;\n  }\n\n  .subtitle {\n    font-size: 1rem;\n  }\n\n  .cartContent {\n    gap: 1.5rem;\n  }\n\n  .additionalActions {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .helpSection,\n  .shippingInfo {\n    padding: 1rem;\n  }\n\n  .emptyIcon {\n    width: 80px;\n    height: 80px;\n  }\n\n  .emptyTitle {\n    font-size: 1.5rem;\n  }\n\n  .emptyMessage {\n    font-size: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0.5rem;\n  }\n\n  .title {\n    font-size: 1.75rem;\n  }\n\n  .itemsHeader {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n\n  .shopNowBtn {\n    padding: 0.875rem 1.5rem;\n    font-size: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAYA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;EACE;;;;;;AAMF;EACE;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA"}}]}