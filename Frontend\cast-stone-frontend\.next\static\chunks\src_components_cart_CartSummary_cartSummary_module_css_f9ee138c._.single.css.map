{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartSummary/cartSummary.module.css"], "sourcesContent": ["/* Cart Summary Styles - Magazine/Editorial Theme */\n.cartSummary {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  position: sticky;\n  top: 2rem;\n  height: fit-content;\n}\n\n.title {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 1.5rem 0;\n  text-align: center;\n  border-bottom: 2px solid #f3f4f6;\n  padding-bottom: 1rem;\n}\n\n/* Summary Details */\n.summaryDetails {\n  margin-bottom: 1.5rem;\n}\n\n.summaryRow {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.label {\n  color: #6b5b4d;\n  font-size: 1rem;\n  font-weight: 500;\n}\n\n.value {\n  color: #4a3728;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.freeShipping {\n  color: #059669;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n.divider {\n  height: 1px;\n  background: #e5e7eb;\n  margin: 1rem 0;\n}\n\n.totalRow {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 0;\n  border-top: 2px solid #4a3728;\n  margin-top: 1rem;\n}\n\n.totalLabel {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n}\n\n.totalValue {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n/* Shipping Notice */\n.shippingNotice {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: #f0f9ff;\n  border: 1px solid #bae6fd;\n  border-radius: 6px;\n  padding: 0.75rem;\n  margin-bottom: 1.5rem;\n  color: #0369a1;\n  font-size: 0.9rem;\n}\n\n.infoIcon {\n  width: 16px;\n  height: 16px;\n  flex-shrink: 0;\n}\n\n/* Action Buttons */\n.actionButtons {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.checkoutBtn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 1rem 1.5rem;\n  background: #8b7355;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-weight: 600;\n  font-size: 1rem;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.checkoutBtn:hover {\n  background: #6d5a47;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);\n}\n\n.checkoutIcon {\n  width: 20px;\n  height: 20px;\n  stroke-width: 2;\n}\n\n.clearBtn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  background: transparent;\n  color: #dc2626;\n  border: 2px solid #dc2626;\n  border-radius: 6px;\n  font-weight: 600;\n  font-size: 0.9rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.clearBtn:hover:not(:disabled) {\n  background: #dc2626;\n  color: white;\n}\n\n.clearBtn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.clearIcon {\n  width: 18px;\n  height: 18px;\n  stroke-width: 2;\n}\n\n/* Security Notice */\n.securityNotice {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  color: #059669;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-align: center;\n}\n\n.securityIcon {\n  width: 16px;\n  height: 16px;\n  stroke-width: 2;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .cartSummary {\n    position: static;\n    margin-top: 2rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .cartSummary {\n    padding: 1.5rem;\n    margin-top: 1.5rem;\n  }\n\n  .title {\n    font-size: 1.25rem;\n    margin-bottom: 1rem;\n  }\n\n  .totalLabel {\n    font-size: 1.1rem;\n  }\n\n  .totalValue {\n    font-size: 1.25rem;\n  }\n\n  .checkoutBtn {\n    padding: 0.875rem 1.25rem;\n    font-size: 0.95rem;\n  }\n\n  .actionButtons {\n    gap: 0.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .cartSummary {\n    padding: 1rem;\n  }\n\n  .summaryRow {\n    margin-bottom: 0.5rem;\n  }\n\n  .label,\n  .value {\n    font-size: 0.9rem;\n  }\n\n  .totalRow {\n    padding: 0.75rem 0;\n  }\n\n  .checkoutBtn {\n    padding: 0.75rem 1rem;\n    font-size: 0.9rem;\n  }\n\n  .clearBtn {\n    padding: 0.625rem 0.875rem;\n    font-size: 0.85rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;;;;AAWA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;;AAiBA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAOA;EACE;;;;;;AAMF;EACE;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;;EAKA"}}]}