[{"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "ClearCart", "RelativePath": "api/Cart/{cartId}/clear", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "UpdateCartItem", "RelativePath": "api/Cart/{cartId}/items/{productId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartId", "Type": "System.Int32", "IsRequired": true}, {"Name": "productId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.UpdateCartItemRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CartResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "RemoveFromCart", "RelativePath": "api/Cart/{cartId}/items/{productId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartId", "Type": "System.Int32", "IsRequired": true}, {"Name": "productId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "AddToCart", "RelativePath": "api/Cart/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.AddToCartRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CartResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "GetOrCreateCart", "RelativePath": "api/Cart/get-or-create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "sessionId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CartResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "RemoveCartItem", "RelativePath": "api/Cart/items/{cartItemId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartItemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "GetCartBySessionId", "RelativePath": "api/Cart/session/{sessionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CartResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "ClearCartBySessionId", "RelativePath": "api/Cart/session/{sessionId}/clear", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "GetCartSummaryBySessionId", "RelativePath": "api/Cart/summary/session/{sessionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CartSummaryResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "GetCartSummaryByUserId", "RelativePath": "api/Cart/summary/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CartSummaryResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "GetCartByUserId", "RelativePath": "api/Cart/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CartResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CartController", "Method": "ClearCartByUserId", "RelativePath": "api/Cart/user/{userId}/clear", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "GetAll", "RelativePath": "api/Collections", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "Create", "RelativePath": "api/Collections", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.CreateCollectionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "GetById", "RelativePath": "api/Collections/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "Update", "RelativePath": "api/Collections/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.UpdateCollectionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "Delete", "RelativePath": "api/Collections/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "GetChildren", "RelativePath": "api/Collections/{id}/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "GetFiltered", "RelativePath": "api/Collections/filter", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Level", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ParentCollectionId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Published", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreatedAfter", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBefore", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedAfter", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBefore", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Tag", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Request.PaginatedResponse`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "GetHierarchy", "RelativePath": "api/Collections/hierarchy", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.CollectionHierarchyResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "GetByLevel", "RelativePath": "api/Collections/level/{level}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "level", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "GetPublished", "RelativePath": "api/Collections/published", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "RefreshRelationships", "RelativePath": "api/Collections/refresh-relationships", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.CollectionsController", "Method": "Search", "RelativePath": "api/Collections/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.CollectionResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetAll", "RelativePath": "api/Orders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.OrderSummaryResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "Create", "RelativePath": "api/Orders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.CreateOrderRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.OrderResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetById", "RelativePath": "api/Orders/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.OrderResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "Delete", "RelativePath": "api/Orders/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "Cancel", "RelativePath": "api/Orders/{id}/cancel", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetDetails", "RelativePath": "api/Orders/{id}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.OrderResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "UpdateStatus", "RelativePath": "api/Orders/{id}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.UpdateOrderStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetByEmail", "RelativePath": "api/Orders/email/{email}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.OrderResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetFiltered", "RelativePath": "api/Orders/filter", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "StatusId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PaymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "Country", "Type": "System.String", "IsRequired": false}, {"Name": "City", "Type": "System.String", "IsRequired": false}, {"Name": "CreatedAfter", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBefore", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Request.PaginatedResponse`1[[Cast_Stone_api.DTOs.Response.OrderResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetPending", "RelativePath": "api/Orders/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.OrderResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetRecent", "RelativePath": "api/Orders/recent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.OrderSummaryResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetRevenueByDateRange", "RelativePath": "api/Orders/revenue/range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetTotalRevenue", "RelativePath": "api/Orders/revenue/total", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetByStatus", "RelativePath": "api/Orders/status/{statusId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "statusId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.OrderResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.OrdersController", "Method": "GetByUserId", "RelativePath": "api/Orders/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.OrderResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "GetAll", "RelativePath": "api/Products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "Create", "RelativePath": "api/Products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.CreateProductRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "GetById", "RelativePath": "api/Products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "Update", "RelativePath": "api/Products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.UpdateProductRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "Delete", "RelativePath": "api/Products/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "UpdateStock", "RelativePath": "api/Products/{id}/stock", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "newStock", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "GetByCollection", "RelativePath": "api/Products/collection/{collectionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "collectionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "GetFeatured", "RelativePath": "api/Products/featured", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.ProductSummaryResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "GetFiltered", "RelativePath": "api/Products/filter", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "CollectionId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinStock", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxStock", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "InStock", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedAfter", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBefore", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedAfter", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBefore", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Tag", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Request.PaginatedResponse`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "GetInStock", "RelativePath": "api/Products/in-stock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "GetLatest", "RelativePath": "api/Products/latest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.ProductSummaryResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "GetByPriceRange", "RelativePath": "api/Products/price-range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "minPrice", "Type": "System.Decimal", "IsRequired": false}, {"Name": "maxPrice", "Type": "System.Decimal", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.ProductsController", "Method": "Search", "RelativePath": "api/Products/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.ProductResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.SeedController", "Method": "SeedAdminUser", "RelativePath": "api/Seed/admin-user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.SeedController", "Method": "SeedAll", "RelativePath": "api/Seed/all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.SeedController", "Method": "SeedCollections", "RelativePath": "api/Seed/collections", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.SeedController", "Method": "SeedProducts", "RelativePath": "api/Seed/products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.SeedController", "Method": "SeedStatuses", "RelativePath": "api/Seed/statuses", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "GetAll", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "Create", "RelativePath": "api/Users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.CreateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "GetById", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "Update", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Cast_Stone_api.DTOs.Request.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "Delete", "RelativePath": "api/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "Activate", "RelativePath": "api/Users/<USER>/activate", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "Deactivate", "RelativePath": "api/Users/<USER>/deactivate", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "GetWithOrders", "RelativePath": "api/Users/<USER>/orders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "GetActive", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "EmailExists", "RelativePath": "api/Users/<USER>/{email}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "GetByEmail", "RelativePath": "api/Users/<USER>/{email}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "GetFiltered", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Role", "Type": "System.String", "IsRequired": false}, {"Name": "Active", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Country", "Type": "System.String", "IsRequired": false}, {"Name": "City", "Type": "System.String", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "CreatedAfter", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBefore", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[Cast_Stone_api.DTOs.Request.PaginatedResponse`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "GetRecent", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Cast_Stone_api.Controllers.UsersController", "Method": "GetByRole", "RelativePath": "api/Users/<USER>/{role}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Cast_Stone_api.DTOs.Response.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[Cast_Stone_api.DTOs.Response.UserResponse, Cast-Stone-api, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]