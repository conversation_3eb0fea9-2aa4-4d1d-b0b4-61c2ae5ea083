{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/HeroSection/heroSection.module.css"], "sourcesContent": ["/* Hero Section Styles */\n.hero {\n  position: relative;\n  height: 100vh;\n  min-height: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background-color: #1a1a1a; /* Fallback color */\n}\n\n/* Image Background Carousel */\n.imageContainer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n}\n\n.imageSlide {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  transition: opacity 1s ease-in-out;\n}\n\n.imageSlide.active {\n  opacity: 1;\n}\n\n.backgroundImage {\n  object-fit: cover;\n  object-position: center;\n  width: 100%;\n  height: 100%;\n}\n\n.imageOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.6) 0%,\n    rgba(0, 0, 0, 0.4) 50%,\n    rgba(0, 0, 0, 0.7) 100%\n  );\n  z-index: 2;\n}\n\n/* Content Styles */\n.container {\n  position: relative;\n  z-index: 3;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  text-align: center;\n}\n\n.content {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.title {\n  font-family: 'Georgia', 'Times New Roman', serif;\n  font-size: 4rem;\n  font-weight: 700;\n  line-height: 1.1;\n  margin-bottom: 1.5rem;\n  color: #d4af8c;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n  letter-spacing: -0.02em;\n}\n\n.titleLine1,\n.titleLine2 {\n  display: block;\n  animation: fadeInUp 1s ease-out forwards;\n}\n\n.titleLine1 {\n  animation-delay: 0.3s;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.titleLine2 {\n  animation-delay: 0.6s;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.subtitle {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 1.25rem;\n  font-weight: 400;\n  line-height: 1.6;\n  color: #ffffff;\n  margin-bottom: 3rem;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);\n  animation: fadeInUp 1s ease-out 0.9s forwards;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n/* Button Styles */\n.actions {\n  display: flex;\n  gap: 1.5rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  animation: fadeInUp 1s ease-out 1.2s forwards;\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.primaryButton,\n.secondaryButton {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 1rem 2.5rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  letter-spacing: 0.1em;\n  text-transform: uppercase;\n  text-decoration: none;\n  border-radius: 50px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  overflow: hidden;\n  cursor: pointer;\n  border: 2px solid transparent;\n  min-width: 200px;\n}\n\n.primaryButton {\n  background: linear-gradient(135deg, #8b4513, #a0522d);\n  color: #ffffff;\n  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);\n}\n\n.primaryButton:hover {\n  background: linear-gradient(135deg, #a0522d, #8b4513);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);\n}\n\n.secondaryButton {\n  background: transparent;\n  color: #ffffff;\n  border-color: #ffffff;\n  backdrop-filter: blur(10px);\n}\n\n.secondaryButton:hover {\n  background: rgba(255, 255, 255, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\n}\n\n.buttonText {\n  position: relative;\n  z-index: 2;\n}\n\n.buttonRipple {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\n  transform: scale(0);\n  transition: transform 0.6s ease-out;\n  z-index: 1;\n}\n\n.primaryButton:active .buttonRipple,\n.secondaryButton:active .buttonRipple {\n  transform: scale(1);\n}\n\n/* Scroll Indicator */\n.scrollIndicator {\n  position: absolute;\n  bottom: 2rem;\n  left: 50%;\n  transform: translateX(-50%);\n  z-index: 3;\n  animation: fadeInUp 1s ease-out 1.5s forwards;\n  opacity: 0;\n}\n\n.scrollArrow {\n  color: #ffffff;\n  animation: bounce 2s infinite;\n  cursor: pointer;\n  transition: color 0.3s ease;\n}\n\n.scrollArrow:hover {\n  color: #d4af8c;\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-10px);\n  }\n  60% {\n    transform: translateY(-5px);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .title {\n    font-size: 3.5rem;\n  }\n\n  .subtitle {\n    font-size: 1.1rem;\n  }\n\n  .container {\n    padding: 0 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .title {\n    font-size: 2.5rem;\n  }\n\n  .subtitle {\n    font-size: 1rem;\n    margin-bottom: 2rem;\n  }\n\n  .actions {\n    flex-direction: column;\n    align-items: center;\n    gap: 1rem;\n  }\n\n  .primaryButton,\n  .secondaryButton {\n    width: 100%;\n    max-width: 280px;\n    padding: 0.875rem 2rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .hero {\n    min-height: 500px;\n  }\n\n  .title {\n    font-size: 2rem;\n    margin-bottom: 1rem;\n  }\n\n  .subtitle {\n    font-size: 0.9rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .primaryButton,\n  .secondaryButton {\n    font-size: 0.8rem;\n    padding: 0.75rem 1.5rem;\n  }\n}\n\n/* Mobile responsive adjustments */\n@media (max-width: 768px) {\n  .hero {\n    min-height: 500px;\n  }\n\n  .imageContainer {\n    /* Images will continue to work on mobile */\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAgBA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;AAKA;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAaA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;EAOA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;;AAQF;EACE"}}]}