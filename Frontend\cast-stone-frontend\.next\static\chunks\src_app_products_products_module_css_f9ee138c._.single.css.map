{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/products/products.module.css"], "sourcesContent": ["/* Products Page Styles - Magazine/Editorial Theme */\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n  min-height: 80vh;\n}\n\n/* Header */\n.header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  margin-bottom: 3rem;\n  padding-bottom: 2rem;\n  border-bottom: 2px solid #f3f4f6;\n}\n\n.headerContent {\n  flex: 1;\n}\n\n.title {\n  color: #4a3728;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  line-height: 1.2;\n}\n\n.subtitle {\n  color: #6b5b4d;\n  font-size: 1.1rem;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.headerActions {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n\n.filterToggle {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  background: #8b7355;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.filterToggle:hover {\n  background: #6d5a47;\n}\n\n.filterIcon {\n  width: 18px;\n  height: 18px;\n  stroke-width: 2;\n}\n\n.resultsCount {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  font-weight: 600;\n  white-space: nowrap;\n}\n\n/* Content Layout */\n.content {\n  display: grid;\n  grid-template-columns: 300px 1fr;\n  gap: 3rem;\n}\n\n/* Filters Sidebar */\n.filtersSidebar {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  height: fit-content;\n  position: sticky;\n  top: 2rem;\n}\n\n.filtersHeader {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.filtersHeader h3 {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0;\n}\n\n.clearFilters {\n  color: #dc2626;\n  background: none;\n  border: none;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  text-decoration: underline;\n}\n\n.clearFilters:hover {\n  color: #b91c1c;\n}\n\n/* Filter Groups */\n.filterGroup {\n  margin-bottom: 2rem;\n}\n\n.filterGroup label {\n  display: block;\n  color: #4a3728;\n  font-weight: 600;\n  margin-bottom: 0.75rem;\n  font-size: 0.9rem;\n}\n\n.searchInput,\n.filterSelect,\n.priceInput {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  transition: border-color 0.2s ease;\n}\n\n.searchInput:focus,\n.filterSelect:focus,\n.priceInput:focus {\n  outline: none;\n  border-color: #8b7355;\n}\n\n.priceRange {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.priceRange span {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.priceInput {\n  flex: 1;\n}\n\n.checkboxLabel {\n  display: flex !important;\n  align-items: center;\n  gap: 0.5rem;\n  cursor: pointer;\n  margin-bottom: 0 !important;\n}\n\n.checkboxLabel input[type=\"checkbox\"] {\n  width: 18px;\n  height: 18px;\n  accent-color: #8b7355;\n}\n\n/* Products Section */\n.productsSection {\n  min-width: 0; /* Allow content to shrink */\n}\n\n/* Mobile Filters */\n@media (max-width: 1024px) {\n  .content {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .filtersSidebar {\n    position: static;\n    display: none;\n    order: -1;\n  }\n\n  .filtersSidebar.showFilters {\n    display: block;\n  }\n\n  .filterToggle {\n    display: flex;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 1rem;\n  }\n\n  .header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1.5rem;\n  }\n\n  .headerActions {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .title {\n    font-size: 2rem;\n  }\n\n  .subtitle {\n    font-size: 1rem;\n  }\n\n  .filtersSidebar {\n    padding: 1.5rem;\n  }\n\n  .content {\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0.5rem;\n  }\n\n  .title {\n    font-size: 1.75rem;\n  }\n\n  .headerActions {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n\n  .filterToggle {\n    justify-content: center;\n  }\n\n  .resultsCount {\n    text-align: center;\n  }\n\n  .filtersSidebar {\n    padding: 1rem;\n  }\n\n  .filtersHeader {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n\n  .priceRange {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .priceRange span {\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;;;AASA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;AAKA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;AAWA;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;AAOA;;;;AAKA;EACE;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;;AAMF;EACE;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA"}}]}