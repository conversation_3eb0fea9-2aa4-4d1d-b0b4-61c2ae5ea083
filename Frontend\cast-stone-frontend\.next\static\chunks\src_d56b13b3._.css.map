{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductCard/productCard.module.css"], "sourcesContent": ["/* Product Card Styles - Magazine/Editorial Theme */\n.productCard {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.productCard:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n/* Image Container */\n.imageContainer {\n  position: relative;\n  width: 100%;\n  height: 250px;\n  overflow: hidden;\n}\n\n.productImage {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.productCard:hover .productImage {\n  transform: scale(1.05);\n}\n\n.outOfStockOverlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n/* Product Info */\n.productInfo {\n  padding: 1.5rem;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.productName {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0 0 0.75rem 0;\n  line-height: 1.3;\n}\n\n.productDescription {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  line-height: 1.5;\n  margin: 0 0 1rem 0;\n  flex: 1;\n}\n\n/* Price Container */\n.priceContainer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.price {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.collection {\n  color: #8b7355;\n  font-size: 0.85rem;\n  font-style: italic;\n}\n\n/* Stock Info */\n.stockInfo {\n  margin-bottom: 1rem;\n}\n\n.inStock {\n  color: #059669;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n.outOfStock {\n  color: #dc2626;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n/* Action Buttons */\n.actionButtons {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n  margin-top: auto;\n}\n\n.viewDetailsBtn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.75rem 1rem;\n  background: transparent;\n  color: #8b7355;\n  border: 2px solid #8b7355;\n  border-radius: 4px;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.viewDetailsBtn:hover {\n  background: #8b7355;\n  color: white;\n}\n\n/* Add to Cart Section */\n.addToCartSection {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.quantitySelector {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.quantityBtn {\n  width: 32px;\n  height: 32px;\n  border: 1px solid #d1d5db;\n  background: white;\n  color: #4a3728;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.quantityBtn:hover:not(:disabled) {\n  background: #f3f4f6;\n  border-color: #8b7355;\n}\n\n.quantityBtn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.quantity {\n  min-width: 40px;\n  text-align: center;\n  font-weight: 600;\n  color: #4a3728;\n}\n\n.addToCartBtn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 0.875rem 1rem;\n  background: #8b7355;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-weight: 600;\n  font-size: 0.9rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.addToCartBtn:hover:not(:disabled) {\n  background: #6d5a47;\n  transform: translateY(-1px);\n}\n\n.addToCartBtn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.cartIcon {\n  width: 18px;\n  height: 18px;\n  stroke-width: 2;\n}\n\n.loading {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.loading::after {\n  content: '';\n  width: 16px;\n  height: 16px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .productCard {\n    margin-bottom: 1rem;\n  }\n  \n  .imageContainer {\n    height: 200px;\n  }\n  \n  .productInfo {\n    padding: 1rem;\n  }\n  \n  .productName {\n    font-size: 1.1rem;\n  }\n  \n  .price {\n    font-size: 1.25rem;\n  }\n  \n  .actionButtons {\n    gap: 0.5rem;\n  }\n  \n  .addToCartSection {\n    flex-direction: column;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAgBA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAOA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductGrid/productGrid.module.css"], "sourcesContent": ["/* Product Grid Styles - Magazine/Editorial Theme */\n.productGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 2rem;\n  padding: 2rem 0;\n}\n\n/* Loading States */\n.loadingContainer {\n  padding: 2rem 0;\n}\n\n.loadingGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.loadingCard {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  animation: pulse 1.5s ease-in-out infinite;\n}\n\n.loadingImage {\n  width: 100%;\n  height: 250px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n.loadingContent {\n  padding: 1.5rem;\n}\n\n.loadingTitle {\n  height: 1.5rem;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n  margin-bottom: 0.75rem;\n}\n\n.loadingDescription {\n  height: 1rem;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n  margin-bottom: 0.5rem;\n  width: 80%;\n}\n\n.loadingPrice {\n  height: 1.25rem;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n  margin-bottom: 1rem;\n  width: 60%;\n}\n\n.loadingButton {\n  height: 2.5rem;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.8;\n  }\n}\n\n/* Empty State */\n.emptyContainer {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  min-height: 300px;\n}\n\n.emptyIcon {\n  width: 80px;\n  height: 80px;\n  color: #d1d5db;\n  margin-bottom: 1.5rem;\n}\n\n.emptyIcon svg {\n  width: 100%;\n  height: 100%;\n}\n\n.emptyTitle {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.75rem 0;\n}\n\n.emptyMessage {\n  color: #6b5b4d;\n  font-size: 1rem;\n  line-height: 1.5;\n  margin: 0;\n  max-width: 400px;\n}\n\n/* Responsive Design */\n@media (max-width: 1200px) {\n  .productGrid,\n  .loadingGrid {\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .productGrid,\n  .loadingGrid {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n    gap: 1rem;\n    padding: 1rem 0;\n  }\n  \n  .emptyContainer {\n    padding: 3rem 1rem;\n  }\n  \n  .emptyIcon {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .emptyTitle {\n    font-size: 1.25rem;\n  }\n  \n  .emptyMessage {\n    font-size: 0.9rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .productGrid,\n  .loadingGrid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AASA;EACE;;;;;;AAOF;EACE;;;;;;EAOA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/products/products.module.css"], "sourcesContent": ["/* Products Page Styles - Magazine/Editorial Theme */\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n  min-height: 80vh;\n}\n\n/* Header */\n.header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  margin-bottom: 3rem;\n  padding-bottom: 2rem;\n  border-bottom: 2px solid #f3f4f6;\n}\n\n.headerContent {\n  flex: 1;\n}\n\n.title {\n  color: #4a3728;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  line-height: 1.2;\n}\n\n.subtitle {\n  color: #6b5b4d;\n  font-size: 1.1rem;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.headerActions {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n\n.filterToggle {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  background: #8b7355;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.filterToggle:hover {\n  background: #6d5a47;\n}\n\n.filterIcon {\n  width: 18px;\n  height: 18px;\n  stroke-width: 2;\n}\n\n.resultsCount {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  font-weight: 600;\n  white-space: nowrap;\n}\n\n/* Content Layout */\n.content {\n  display: grid;\n  grid-template-columns: 300px 1fr;\n  gap: 3rem;\n}\n\n/* Filters Sidebar */\n.filtersSidebar {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  height: fit-content;\n  position: sticky;\n  top: 2rem;\n}\n\n.filtersHeader {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.filtersHeader h3 {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0;\n}\n\n.clearFilters {\n  color: #dc2626;\n  background: none;\n  border: none;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  text-decoration: underline;\n}\n\n.clearFilters:hover {\n  color: #b91c1c;\n}\n\n/* Filter Groups */\n.filterGroup {\n  margin-bottom: 2rem;\n}\n\n.filterGroup label {\n  display: block;\n  color: #4a3728;\n  font-weight: 600;\n  margin-bottom: 0.75rem;\n  font-size: 0.9rem;\n}\n\n.searchInput,\n.filterSelect,\n.priceInput {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  transition: border-color 0.2s ease;\n}\n\n.searchInput:focus,\n.filterSelect:focus,\n.priceInput:focus {\n  outline: none;\n  border-color: #8b7355;\n}\n\n.priceRange {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.priceRange span {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.priceInput {\n  flex: 1;\n}\n\n.checkboxLabel {\n  display: flex !important;\n  align-items: center;\n  gap: 0.5rem;\n  cursor: pointer;\n  margin-bottom: 0 !important;\n}\n\n.checkboxLabel input[type=\"checkbox\"] {\n  width: 18px;\n  height: 18px;\n  accent-color: #8b7355;\n}\n\n/* Products Section */\n.productsSection {\n  min-width: 0; /* Allow content to shrink */\n}\n\n/* Mobile Filters */\n@media (max-width: 1024px) {\n  .content {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .filtersSidebar {\n    position: static;\n    display: none;\n    order: -1;\n  }\n\n  .filtersSidebar.showFilters {\n    display: block;\n  }\n\n  .filterToggle {\n    display: flex;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 1rem;\n  }\n\n  .header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1.5rem;\n  }\n\n  .headerActions {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .title {\n    font-size: 2rem;\n  }\n\n  .subtitle {\n    font-size: 1rem;\n  }\n\n  .filtersSidebar {\n    padding: 1.5rem;\n  }\n\n  .content {\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0.5rem;\n  }\n\n  .title {\n    font-size: 1.75rem;\n  }\n\n  .headerActions {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n\n  .filterToggle {\n    justify-content: center;\n  }\n\n  .resultsCount {\n    text-align: center;\n  }\n\n  .filtersSidebar {\n    padding: 1rem;\n  }\n\n  .filtersHeader {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n\n  .priceRange {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .priceRange span {\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;;;AASA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;AAKA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;AAWA;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;AAOA;;;;AAKA;EACE;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;;AAMF;EACE;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA", "debugId": null}}]}