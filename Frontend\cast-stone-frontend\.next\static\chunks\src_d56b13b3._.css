/* [project]/src/components/products/ProductCard/productCard.module.css [app-client] (css) */
.productCard-module__UIKE7W__productCard {
  background: #fff;
  border-radius: 8px;
  flex-direction: column;
  height: 100%;
  transition: transform .3s, box-shadow .3s;
  display: flex;
  overflow: hidden;
  box-shadow: 0 2px 8px #0000001a;
}

.productCard-module__UIKE7W__productCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px #00000026;
}

.productCard-module__UIKE7W__imageContainer {
  width: 100%;
  height: 250px;
  position: relative;
  overflow: hidden;
}

.productCard-module__UIKE7W__productImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.productCard-module__UIKE7W__productCard:hover .productCard-module__UIKE7W__productImage {
  transform: scale(1.05);
}

.productCard-module__UIKE7W__outOfStockOverlay {
  color: #fff;
  background: #000000b3;
  justify-content: center;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  position: absolute;
  inset: 0;
}

.productCard-module__UIKE7W__productInfo {
  flex-direction: column;
  flex: 1;
  padding: 1.5rem;
  display: flex;
}

.productCard-module__UIKE7W__productName {
  color: #4a3728;
  margin: 0 0 .75rem;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.3;
}

.productCard-module__UIKE7W__productDescription {
  color: #6b5b4d;
  flex: 1;
  margin: 0 0 1rem;
  font-size: .9rem;
  line-height: 1.5;
}

.productCard-module__UIKE7W__priceContainer {
  justify-content: space-between;
  align-items: center;
  margin-bottom: .75rem;
  display: flex;
}

.productCard-module__UIKE7W__price {
  color: #4a3728;
  font-size: 1.5rem;
  font-weight: 700;
}

.productCard-module__UIKE7W__collection {
  color: #8b7355;
  font-size: .85rem;
  font-style: italic;
}

.productCard-module__UIKE7W__stockInfo {
  margin-bottom: 1rem;
}

.productCard-module__UIKE7W__inStock {
  color: #059669;
  font-size: .85rem;
  font-weight: 600;
}

.productCard-module__UIKE7W__outOfStock {
  color: #dc2626;
  font-size: .85rem;
  font-weight: 600;
}

.productCard-module__UIKE7W__actionButtons {
  flex-direction: column;
  gap: .75rem;
  margin-top: auto;
  display: flex;
}

.productCard-module__UIKE7W__viewDetailsBtn {
  color: #8b7355;
  background: none;
  border: 2px solid #8b7355;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  padding: .75rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: inline-flex;
}

.productCard-module__UIKE7W__viewDetailsBtn:hover {
  color: #fff;
  background: #8b7355;
}

.productCard-module__UIKE7W__addToCartSection {
  flex-direction: column;
  gap: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__quantitySelector {
  justify-content: center;
  align-items: center;
  gap: .5rem;
  margin-bottom: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__quantityBtn {
  color: #4a3728;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  font-weight: 600;
  transition: all .2s;
  display: flex;
}

.productCard-module__UIKE7W__quantityBtn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #8b7355;
}

.productCard-module__UIKE7W__quantityBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.productCard-module__UIKE7W__quantity {
  text-align: center;
  color: #4a3728;
  min-width: 40px;
  font-weight: 600;
}

.productCard-module__UIKE7W__addToCartBtn {
  color: #fff;
  cursor: pointer;
  background: #8b7355;
  border: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: .875rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.productCard-module__UIKE7W__addToCartBtn:hover:not(:disabled) {
  background: #6d5a47;
  transform: translateY(-1px);
}

.productCard-module__UIKE7W__addToCartBtn:disabled {
  opacity: .7;
  cursor: not-allowed;
  transform: none;
}

.productCard-module__UIKE7W__cartIcon {
  stroke-width: 2px;
  width: 18px;
  height: 18px;
}

.productCard-module__UIKE7W__loading {
  align-items: center;
  gap: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__loading:after {
  content: "";
  border: 2px solid #0000;
  border-top-color: currentColor;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: 1s linear infinite productCard-module__UIKE7W__spin;
}

@keyframes productCard-module__UIKE7W__spin {
  to {
    transform: rotate(360deg);
  }
}

@media (width <= 768px) {
  .productCard-module__UIKE7W__productCard {
    margin-bottom: 1rem;
  }

  .productCard-module__UIKE7W__imageContainer {
    height: 200px;
  }

  .productCard-module__UIKE7W__productInfo {
    padding: 1rem;
  }

  .productCard-module__UIKE7W__productName {
    font-size: 1.1rem;
  }

  .productCard-module__UIKE7W__price {
    font-size: 1.25rem;
  }

  .productCard-module__UIKE7W__actionButtons {
    gap: .5rem;
  }

  .productCard-module__UIKE7W__addToCartSection {
    flex-direction: column;
  }
}


/* [project]/src/components/products/ProductGrid/productGrid.module.css [app-client] (css) */
.productGrid-module__d3162q__productGrid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
  display: grid;
}

.productGrid-module__d3162q__loadingContainer {
  padding: 2rem 0;
}

.productGrid-module__d3162q__loadingGrid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  display: grid;
}

.productGrid-module__d3162q__loadingCard {
  background: #fff;
  border-radius: 8px;
  animation: 1.5s ease-in-out infinite productGrid-module__d3162q__pulse;
  overflow: hidden;
  box-shadow: 0 2px 8px #0000001a;
}

.productGrid-module__d3162q__loadingImage {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  width: 100%;
  height: 250px;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

.productGrid-module__d3162q__loadingContent {
  padding: 1.5rem;
}

.productGrid-module__d3162q__loadingTitle {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  height: 1.5rem;
  margin-bottom: .75rem;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

.productGrid-module__d3162q__loadingDescription {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  width: 80%;
  height: 1rem;
  margin-bottom: .5rem;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

.productGrid-module__d3162q__loadingPrice {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  width: 60%;
  height: 1.25rem;
  margin-bottom: 1rem;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

.productGrid-module__d3162q__loadingButton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  height: 2.5rem;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

@keyframes productGrid-module__d3162q__shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes productGrid-module__d3162q__pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .8;
  }
}

.productGrid-module__d3162q__emptyContainer {
  text-align: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 4rem 2rem;
  display: flex;
}

.productGrid-module__d3162q__emptyIcon {
  color: #d1d5db;
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
}

.productGrid-module__d3162q__emptyIcon svg {
  width: 100%;
  height: 100%;
}

.productGrid-module__d3162q__emptyTitle {
  color: #4a3728;
  margin: 0 0 .75rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.productGrid-module__d3162q__emptyMessage {
  color: #6b5b4d;
  max-width: 400px;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

@media (width <= 1200px) {
  .productGrid-module__d3162q__productGrid, .productGrid-module__d3162q__loadingGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (width <= 768px) {
  .productGrid-module__d3162q__productGrid, .productGrid-module__d3162q__loadingGrid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    padding: 1rem 0;
  }

  .productGrid-module__d3162q__emptyContainer {
    padding: 3rem 1rem;
  }

  .productGrid-module__d3162q__emptyIcon {
    width: 60px;
    height: 60px;
  }

  .productGrid-module__d3162q__emptyTitle {
    font-size: 1.25rem;
  }

  .productGrid-module__d3162q__emptyMessage {
    font-size: .9rem;
  }
}

@media (width <= 480px) {
  .productGrid-module__d3162q__productGrid, .productGrid-module__d3162q__loadingGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}


/* [project]/src/app/products/products.module.css [app-client] (css) */
.products-module__E8alaG__container {
  max-width: 1400px;
  min-height: 80vh;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.products-module__E8alaG__header {
  border-bottom: 2px solid #f3f4f6;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  display: flex;
}

.products-module__E8alaG__headerContent {
  flex: 1;
}

.products-module__E8alaG__title {
  color: #4a3728;
  margin: 0 0 .5rem;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.products-module__E8alaG__subtitle {
  color: #6b5b4d;
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.5;
}

.products-module__E8alaG__headerActions {
  align-items: center;
  gap: 1.5rem;
  display: flex;
}

.products-module__E8alaG__filterToggle {
  color: #fff;
  cursor: pointer;
  background: #8b7355;
  border: none;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  padding: .75rem 1rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.products-module__E8alaG__filterToggle:hover {
  background: #6d5a47;
}

.products-module__E8alaG__filterIcon {
  stroke-width: 2px;
  width: 18px;
  height: 18px;
}

.products-module__E8alaG__resultsCount {
  color: #6b5b4d;
  white-space: nowrap;
  font-size: .9rem;
  font-weight: 600;
}

.products-module__E8alaG__content {
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  display: grid;
}

.products-module__E8alaG__filtersSidebar {
  background: #fff;
  border-radius: 8px;
  height: fit-content;
  padding: 2rem;
  position: sticky;
  top: 2rem;
  box-shadow: 0 2px 8px #0000001a;
}

.products-module__E8alaG__filtersHeader {
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  display: flex;
}

.products-module__E8alaG__filtersHeader h3 {
  color: #4a3728;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
}

.products-module__E8alaG__clearFilters {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: none;
  font-size: .9rem;
  font-weight: 600;
  text-decoration: underline;
}

.products-module__E8alaG__clearFilters:hover {
  color: #b91c1c;
}

.products-module__E8alaG__filterGroup {
  margin-bottom: 2rem;
}

.products-module__E8alaG__filterGroup label {
  color: #4a3728;
  margin-bottom: .75rem;
  font-size: .9rem;
  font-weight: 600;
  display: block;
}

.products-module__E8alaG__searchInput, .products-module__E8alaG__filterSelect, .products-module__E8alaG__priceInput {
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  width: 100%;
  padding: .75rem;
  font-size: .9rem;
  transition: border-color .2s;
}

.products-module__E8alaG__searchInput:focus, .products-module__E8alaG__filterSelect:focus, .products-module__E8alaG__priceInput:focus {
  border-color: #8b7355;
  outline: none;
}

.products-module__E8alaG__priceRange {
  align-items: center;
  gap: .5rem;
  display: flex;
}

.products-module__E8alaG__priceRange span {
  color: #6b5b4d;
  font-size: .9rem;
  font-weight: 500;
}

.products-module__E8alaG__priceInput {
  flex: 1;
}

.products-module__E8alaG__checkboxLabel {
  cursor: pointer;
  align-items: center;
  gap: .5rem;
  margin-bottom: 0 !important;
  display: flex !important;
}

.products-module__E8alaG__checkboxLabel input[type="checkbox"] {
  accent-color: #8b7355;
  width: 18px;
  height: 18px;
}

.products-module__E8alaG__productsSection {
  min-width: 0;
}

@media (width <= 1024px) {
  .products-module__E8alaG__content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .products-module__E8alaG__filtersSidebar {
    order: -1;
    display: none;
    position: static;
  }

  .products-module__E8alaG__filtersSidebar.products-module__E8alaG__showFilters {
    display: block;
  }

  .products-module__E8alaG__filterToggle {
    display: flex;
  }
}

@media (width <= 768px) {
  .products-module__E8alaG__container {
    padding: 1rem;
  }

  .products-module__E8alaG__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .products-module__E8alaG__headerActions {
    justify-content: space-between;
    width: 100%;
  }

  .products-module__E8alaG__title {
    font-size: 2rem;
  }

  .products-module__E8alaG__subtitle {
    font-size: 1rem;
  }

  .products-module__E8alaG__filtersSidebar {
    padding: 1.5rem;
  }

  .products-module__E8alaG__content {
    gap: 1.5rem;
  }
}

@media (width <= 480px) {
  .products-module__E8alaG__container {
    padding: .5rem;
  }

  .products-module__E8alaG__title {
    font-size: 1.75rem;
  }

  .products-module__E8alaG__headerActions {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .products-module__E8alaG__filterToggle {
    justify-content: center;
  }

  .products-module__E8alaG__resultsCount {
    text-align: center;
  }

  .products-module__E8alaG__filtersSidebar {
    padding: 1rem;
  }

  .products-module__E8alaG__filtersHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .products-module__E8alaG__priceRange {
    flex-direction: column;
    align-items: stretch;
  }

  .products-module__E8alaG__priceRange span {
    text-align: center;
  }
}


/*# sourceMappingURL=src_d56b13b3._.css.map*/