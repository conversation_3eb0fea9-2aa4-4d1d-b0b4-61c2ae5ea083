{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductGrid/productGrid.module.css"], "sourcesContent": ["/* Product Grid Styles - Magazine/Editorial Theme */\n.productGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 2rem;\n  padding: 2rem 0;\n}\n\n/* Loading States */\n.loadingContainer {\n  padding: 2rem 0;\n}\n\n.loadingGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.loadingCard {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  animation: pulse 1.5s ease-in-out infinite;\n}\n\n.loadingImage {\n  width: 100%;\n  height: 250px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n.loadingContent {\n  padding: 1.5rem;\n}\n\n.loadingTitle {\n  height: 1.5rem;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n  margin-bottom: 0.75rem;\n}\n\n.loadingDescription {\n  height: 1rem;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n  margin-bottom: 0.5rem;\n  width: 80%;\n}\n\n.loadingPrice {\n  height: 1.25rem;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n  margin-bottom: 1rem;\n  width: 60%;\n}\n\n.loadingButton {\n  height: 2.5rem;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.8;\n  }\n}\n\n/* Empty State */\n.emptyContainer {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  min-height: 300px;\n}\n\n.emptyIcon {\n  width: 80px;\n  height: 80px;\n  color: #d1d5db;\n  margin-bottom: 1.5rem;\n}\n\n.emptyIcon svg {\n  width: 100%;\n  height: 100%;\n}\n\n.emptyTitle {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.75rem 0;\n}\n\n.emptyMessage {\n  color: #6b5b4d;\n  font-size: 1rem;\n  line-height: 1.5;\n  margin: 0;\n  max-width: 400px;\n}\n\n/* Responsive Design */\n@media (max-width: 1200px) {\n  .productGrid,\n  .loadingGrid {\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .productGrid,\n  .loadingGrid {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n    gap: 1rem;\n    padding: 1rem 0;\n  }\n  \n  .emptyContainer {\n    padding: 3rem 1rem;\n  }\n  \n  .emptyIcon {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .emptyTitle {\n    font-size: 1.25rem;\n  }\n  \n  .emptyMessage {\n    font-size: 0.9rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .productGrid,\n  .loadingGrid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AASA;EACE;;;;;;AAOF;EACE;;;;;;EAOA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE"}}]}