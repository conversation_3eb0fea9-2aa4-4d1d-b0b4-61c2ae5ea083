{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartItem/cartItem.module.css"], "sourcesContent": ["/* Cart Item Styles - Magazine/Editorial Theme */\n.cartItem {\n  display: grid;\n  grid-template-columns: 120px 1fr auto auto auto;\n  gap: 1.5rem;\n  padding: 1.5rem;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  align-items: start;\n  transition: box-shadow 0.3s ease;\n}\n\n.cartItem:hover {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n/* Product Image */\n.imageContainer {\n  width: 120px;\n  height: 120px;\n  border-radius: 8px;\n  overflow: hidden;\n  background: #f8f9fa;\n}\n\n.productImage {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* Product Details */\n.productDetails {\n  min-width: 0; /* Allow text truncation */\n}\n\n.productName {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  line-height: 1.3;\n}\n\n.productDescription {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  line-height: 1.5;\n  margin: 0 0 0.75rem 0;\n}\n\n.productMeta {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  margin-bottom: 0.5rem;\n}\n\n.unitPrice {\n  color: #4a3728;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.collection {\n  color: #8b7355;\n  font-size: 0.85rem;\n  font-style: italic;\n}\n\n.stockStatus {\n  margin-top: 0.5rem;\n}\n\n.inStock {\n  color: #059669;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n.outOfStock {\n  color: #dc2626;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n/* Quantity Section */\n.quantitySection {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.5rem;\n  min-width: 120px;\n}\n\n.quantityLabel {\n  color: #4a3728;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-align: center;\n}\n\n.quantityControls {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 0.25rem;\n}\n\n.quantityBtn {\n  width: 32px;\n  height: 32px;\n  border: 1px solid #d1d5db;\n  background: white;\n  color: #4a3728;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.quantityBtn:hover:not(:disabled) {\n  background: #8b7355;\n  color: white;\n  border-color: #8b7355;\n}\n\n.quantityBtn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.quantity {\n  min-width: 40px;\n  text-align: center;\n  font-weight: 600;\n  color: #4a3728;\n  font-size: 1rem;\n}\n\n.updating {\n  color: #8b7355;\n  font-size: 0.75rem;\n  font-style: italic;\n}\n\n/* Price Section */\n.priceSection {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 0.25rem;\n  min-width: 120px;\n}\n\n.itemTotal {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.priceBreakdown {\n  color: #6b5b4d;\n  font-size: 0.85rem;\n}\n\n/* Remove Section */\n.removeSection {\n  display: flex;\n  align-items: flex-start;\n}\n\n.removeBtn {\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: transparent;\n  color: #dc2626;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.removeBtn:hover:not(:disabled) {\n  background: #fee2e2;\n  color: #b91c1c;\n}\n\n.removeBtn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.removeBtn svg {\n  width: 20px;\n  height: 20px;\n  stroke-width: 2;\n}\n\n.removing {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .cartItem {\n    grid-template-columns: 80px 1fr;\n    grid-template-areas: \n      \"image details\"\n      \"image quantity\"\n      \"image price\"\n      \"remove remove\";\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .imageContainer {\n    grid-area: image;\n    width: 80px;\n    height: 80px;\n  }\n\n  .productDetails {\n    grid-area: details;\n  }\n\n  .quantitySection {\n    grid-area: quantity;\n    flex-direction: row;\n    align-items: center;\n    justify-content: flex-start;\n    min-width: auto;\n  }\n\n  .quantityLabel {\n    margin-right: 0.5rem;\n  }\n\n  .priceSection {\n    grid-area: price;\n    align-items: flex-start;\n    min-width: auto;\n  }\n\n  .removeSection {\n    grid-area: remove;\n    justify-content: center;\n    margin-top: 0.5rem;\n  }\n\n  .productName {\n    font-size: 1.1rem;\n  }\n\n  .itemTotal {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .cartItem {\n    grid-template-columns: 1fr;\n    grid-template-areas: \n      \"image\"\n      \"details\"\n      \"quantity\"\n      \"price\"\n      \"remove\";\n    text-align: center;\n  }\n\n  .imageContainer {\n    width: 120px;\n    height: 120px;\n    margin: 0 auto;\n  }\n\n  .quantitySection {\n    justify-content: center;\n  }\n\n  .priceSection {\n    align-items: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;;;;AAQA;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;;;;;;AAeA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAMA;EACE;;;;;;;;;;EAWA;;;;;;EAMA;;;;EAIA;;;;;;;;EAQA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;;AAKF;EACE;;;;;;;;;;EAWA;;;;;;EAMA;;;;EAIA"}}]}