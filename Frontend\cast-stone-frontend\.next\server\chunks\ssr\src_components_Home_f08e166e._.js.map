{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/HeroSection/heroSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actions\": \"heroSection-module__CcYIFG__actions\",\n  \"active\": \"heroSection-module__CcYIFG__active\",\n  \"backgroundImage\": \"heroSection-module__CcYIFG__backgroundImage\",\n  \"bounce\": \"heroSection-module__CcYIFG__bounce\",\n  \"buttonRipple\": \"heroSection-module__CcYIFG__buttonRipple\",\n  \"buttonText\": \"heroSection-module__CcYIFG__buttonText\",\n  \"container\": \"heroSection-module__CcYIFG__container\",\n  \"content\": \"heroSection-module__CcYIFG__content\",\n  \"fadeInUp\": \"heroSection-module__CcYIFG__fadeInUp\",\n  \"hero\": \"heroSection-module__CcYIFG__hero\",\n  \"imageContainer\": \"heroSection-module__CcYIFG__imageContainer\",\n  \"imageOverlay\": \"heroSection-module__CcYIFG__imageOverlay\",\n  \"imageSlide\": \"heroSection-module__CcYIFG__imageSlide\",\n  \"primaryButton\": \"heroSection-module__CcYIFG__primaryButton\",\n  \"scrollArrow\": \"heroSection-module__CcYIFG__scrollArrow\",\n  \"scrollIndicator\": \"heroSection-module__CcYIFG__scrollIndicator\",\n  \"secondaryButton\": \"heroSection-module__CcYIFG__secondaryButton\",\n  \"subtitle\": \"heroSection-module__CcYIFG__subtitle\",\n  \"title\": \"heroSection-module__CcYIFG__title\",\n  \"titleLine1\": \"heroSection-module__CcYIFG__titleLine1\",\n  \"titleLine2\": \"heroSection-module__CcYIFG__titleLine2\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HeroSection/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport styles from './heroSection.module.css';\n\ninterface HeroSectionProps {\n  title?: string;\n  subtitle?: string;\n}\n\nconst HeroSection: React.FC<HeroSectionProps> = ({\n  subtitle = \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\"\n}) => {\n  // Array of hero images\n  const heroImages = [\n    '/heroSection/BANNER IMAGES/IMG_0930.jpg',\n    '/heroSection/BANNER IMAGES/IMG_1194.jpg',\n    '/heroSection/BANNER IMAGES/IMG_1206.jpg',\n    '/heroSection/BANNER IMAGES/IMG_1263.jpg',\n    '/heroSection/BANNER IMAGES/IMG_1686.JPG',\n    '/heroSection/BANNER IMAGES/IMG_2231.jpg',\n    '/heroSection/BANNER IMAGES/IMG_3920.jpg',\n    '/heroSection/BANNER IMAGES/IMG_8272.jpg'\n  ];\n\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n\n  useEffect(() => {\n    // Change image every 3 seconds\n    const interval = setInterval(() => {\n      setCurrentImageIndex((prevIndex) =>\n        (prevIndex + 1) % heroImages.length\n      );\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, [heroImages.length]);\n\n  return (\n    <section className={styles.hero}>\n      {/* Image Background Carousel */}\n      <div className={styles.imageContainer}>\n        {heroImages.map((imageSrc, index) => (\n          <div\n            key={index}\n            className={`${styles.imageSlide} ${\n              index === currentImageIndex ? styles.active : ''\n            }`}\n          >\n            <Image\n              src={imageSrc}\n              alt={`Hero background ${index + 1}`}\n              fill\n              className={styles.backgroundImage}\n              sizes=\"100vw\"\n              priority={index === 0} // Prioritize loading the first image\n              quality={90}\n            />\n          </div>\n        ))}\n\n        {/* Image Overlay */}\n        <div className={styles.imageOverlay}></div>\n      </div>\n\n      {/* Content */}\n      <div className={styles.container}>\n        <div className={styles.content}>\n          <h1 className={styles.title}>\n            <span className={styles.titleLine1}>Timeless Elegance in</span>\n            <span className={styles.titleLine2}>Cast Stone</span>\n          </h1>\n\n          <p className={styles.subtitle}>\n            {subtitle}\n          </p>\n\n          <div className={styles.actions}>\n            <Link href=\"/collections\" className={styles.primaryButton}>\n              <span className={styles.buttonText}>EXPLORE COLLECTION</span>\n              <div className={styles.buttonRipple}></div>\n            </Link>\n\n            <Link href=\"/our-story\" className={styles.secondaryButton}>\n              <span className={styles.buttonText}>WATCH OUR STORY</span>\n              <div className={styles.buttonRipple}></div>\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className={styles.scrollIndicator}>\n        <div className={styles.scrollArrow}>\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n            <path d=\"M7 13L12 18L17 13\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            <path d=\"M7 6L12 11L17 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n          </svg>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,cAA0C,CAAC,EAC/C,WAAW,qJAAqJ,EACjK;IACC,uBAAuB;IACvB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,MAAM,WAAW,YAAY;YAC3B,qBAAqB,CAAC,YACpB,CAAC,YAAY,CAAC,IAAI,WAAW,MAAM;QAEvC,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,WAAW,MAAM;KAAC;IAEtB,qBACE,8OAAC;QAAQ,WAAW,mKAAA,CAAA,UAAM,CAAC,IAAI;;0BAE7B,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,cAAc;;oBAClC,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;4BAEC,WAAW,GAAG,mKAAA,CAAA,UAAM,CAAC,UAAU,CAAC,CAAC,EAC/B,UAAU,oBAAoB,mKAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC9C;sCAEF,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK,CAAC,gBAAgB,EAAE,QAAQ,GAAG;gCACnC,IAAI;gCACJ,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;gCACjC,OAAM;gCACN,UAAU,UAAU;gCACpB,SAAS;;;;;;2BAZN;;;;;kCAkBT,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;0BAIrC,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,SAAS;0BAC9B,cAAA,8OAAC;oBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,OAAO;;sCAC5B,8OAAC;4BAAG,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;;8CACzB,8OAAC;oCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;;;;;;;sCAGtC,8OAAC;4BAAE,WAAW,mKAAA,CAAA,UAAM,CAAC,QAAQ;sCAC1B;;;;;;sCAGH,8OAAC;4BAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,OAAO;;8CAC5B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAe,WAAW,mKAAA,CAAA,UAAM,CAAC,aAAa;;sDACvD,8OAAC;4CAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;sDAAE;;;;;;sDACpC,8OAAC;4CAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;8CAGrC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;;sDACvD,8OAAC;4CAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;sDAAE;;;;;;sDACpC,8OAAC;4CAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;0BACpC,cAAA,8OAAC;oBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,WAAW;8BAChC,cAAA,8OAAC;wBAAI,OAAM;wBAAK,QAAO;wBAAK,SAAQ;wBAAY,MAAK;;0CACnD,8OAAC;gCAAK,GAAE;gCAAoB,QAAO;gCAAe,aAAY;gCAAI,eAAc;gCAAQ,gBAAe;;;;;;0CACvG,8OAAC;gCAAK,GAAE;gCAAkB,QAAO;gCAAe,aAAY;gCAAI,eAAc;gCAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjH;uCAEe", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CategoriesSection/categoriesSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionButton\": \"categoriesSection-module__fGUJra__actionButton\",\n  \"cardActions\": \"categoriesSection-module__fGUJra__cardActions\",\n  \"cardContent\": \"categoriesSection-module__fGUJra__cardContent\",\n  \"cardHeader\": \"categoriesSection-module__fGUJra__cardHeader\",\n  \"cardLink\": \"categoriesSection-module__fGUJra__cardLink\",\n  \"categoriesSection\": \"categoriesSection-module__fGUJra__categoriesSection\",\n  \"categoryCard\": \"categoriesSection-module__fGUJra__categoryCard\",\n  \"categoryDescription\": \"categoriesSection-module__fGUJra__categoryDescription\",\n  \"categoryImage\": \"categoriesSection-module__fGUJra__categoryImage\",\n  \"categorySubtitle\": \"categoriesSection-module__fGUJra__categorySubtitle\",\n  \"categoryTitle\": \"categoriesSection-module__fGUJra__categoryTitle\",\n  \"container\": \"categoriesSection-module__fGUJra__container\",\n  \"grid\": \"categoriesSection-module__fGUJra__grid\",\n  \"header\": \"categoriesSection-module__fGUJra__header\",\n  \"hoverEffect\": \"categoriesSection-module__fGUJra__hoverEffect\",\n  \"imageContainer\": \"categoriesSection-module__fGUJra__imageContainer\",\n  \"imageOverlay\": \"categoriesSection-module__fGUJra__imageOverlay\",\n  \"secondaryButton\": \"categoriesSection-module__fGUJra__secondaryButton\",\n  \"sectionSubtitle\": \"categoriesSection-module__fGUJra__sectionSubtitle\",\n  \"sectionTitle\": \"categoriesSection-module__fGUJra__sectionTitle\",\n  \"stats\": \"categoriesSection-module__fGUJra__stats\",\n  \"statsLabel\": \"categoriesSection-module__fGUJra__statsLabel\",\n  \"statsNumber\": \"categoriesSection-module__fGUJra__statsNumber\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CategoriesSection/CategoriesSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport styles from './categoriesSection.module.css';\n\ninterface Category {\n  id: string;\n  title: string;\n  subtitle: string;\n  description: string;\n  image: string;\n  href: string;\n  stats: {\n    projects: number;\n    label: string;\n  };\n}\n\ninterface CategoriesSectionProps {\n  categories?: Category[];\n}\n\nconst defaultCategories: Category[] = [\n  {\n    id: '911',\n    title: '911',\n    subtitle: 'ARCHITECTURAL',\n    description: 'Timeless architectural elements that define spaces with classical elegance and modern sophistication.',\n    image: '/images/categories/architectural.jpg',\n    href: '/collections/architectural',\n    stats: {\n      projects: 911,\n      label: 'Projects'\n    }\n  },\n  {\n    id: '718',\n    title: '718',\n    subtitle: 'DESIGNER',\n    description: 'Curated designer pieces that blend contemporary aesthetics with traditional craftsmanship.',\n    image: '/images/categories/designer.jpg',\n    href: '/collections/designer',\n    stats: {\n      projects: 718,\n      label: 'Projects'\n    }\n  },\n  {\n    id: 'taycan',\n    title: 'Taycan',\n    subtitle: 'OUTDOOR',\n    description: 'Weather-resistant outdoor elements designed to enhance exterior spaces with lasting beauty.',\n    image: '/images/categories/outdoor.jpg',\n    href: '/collections/outdoor',\n    stats: {\n      projects: 156,\n      label: 'Projects'\n    }\n  },\n  {\n    id: 'panamera',\n    title: 'Panamera',\n    subtitle: 'INTERIOR',\n    description: 'Sophisticated interior accents that transform living spaces into works of art.',\n    image: '/images/categories/interior.jpg',\n    href: '/collections/interior',\n    stats: {\n      projects: 342,\n      label: 'Projects'\n    }\n  }\n];\n\nconst CategoriesSection: React.FC<CategoriesSectionProps> = ({ \n  categories = defaultCategories \n}) => {\n  return (\n    <section className={styles.categoriesSection}>\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <h2 className={styles.sectionTitle}>Our Collections</h2>\n          <p className={styles.sectionSubtitle}>\n            Explore our diverse range of handcrafted cast stone elements, \n            each designed to bring timeless elegance to your space.\n          </p>\n        </div>\n\n        <div className={styles.grid}>\n          {categories.map((category, index) => (\n            <div \n              key={category.id} \n              className={`${styles.categoryCard} ${styles[`card${index + 1}`]}`}\n            >\n              <Link href={category.href} className={styles.cardLink}>\n                <div className={styles.imageContainer}>\n                  <Image\n                    src={category.image}\n                    alt={category.title}\n                    fill\n                    className={styles.categoryImage}\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw\"\n                  />\n                  <div className={styles.imageOverlay}></div>\n                </div>\n\n                <div className={styles.cardContent}>\n                  <div className={styles.cardHeader}>\n                    <div className={styles.stats}>\n                      <span className={styles.statsNumber}>{category.stats.projects}</span>\n                      <span className={styles.statsLabel}>{category.stats.label}</span>\n                    </div>\n                    <h3 className={styles.categoryTitle}>{category.title}</h3>\n                    <p className={styles.categorySubtitle}>{category.subtitle}</p>\n                  </div>\n\n                  <p className={styles.categoryDescription}>\n                    {category.description}\n                  </p>\n\n                  <div className={styles.cardActions}>\n                    <button className={styles.actionButton}>\n                      <span>Shop Now</span>\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    </button>\n                    <button className={styles.secondaryButton}>\n                      <span>All Projects</span>\n                    </button>\n                  </div>\n                </div>\n\n                <div className={styles.hoverEffect}></div>\n              </Link>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default CategoriesSection;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAwBA,MAAM,oBAAgC;IACpC;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,MAAM;QACN,OAAO;YACL,UAAU;YACV,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,MAAM;QACN,OAAO;YACL,UAAU;YACV,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,MAAM;QACN,OAAO;YACL,UAAU;YACV,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,MAAM;QACN,OAAO;YACL,UAAU;YACV,OAAO;QACT;IACF;CACD;AAED,MAAM,oBAAsD,CAAC,EAC3D,aAAa,iBAAiB,EAC/B;IACC,qBACE,8OAAC;QAAQ,WAAW,+KAAA,CAAA,UAAM,CAAC,iBAAiB;kBAC1C,cAAA,8OAAC;YAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,MAAM;;sCAC3B,8OAAC;4BAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;sCACpC,8OAAC;4BAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,eAAe;sCAAE;;;;;;;;;;;;8BAMxC,8OAAC;oBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,IAAI;8BACxB,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;4BAEC,WAAW,GAAG,+KAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,+KAAA,CAAA,UAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE;sCAEjE,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,SAAS,IAAI;gCAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,QAAQ;;kDACnD,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;0DACnC,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,SAAS,KAAK;gDACnB,KAAK,SAAS,KAAK;gDACnB,IAAI;gDACJ,WAAW,+KAAA,CAAA,UAAM,CAAC,aAAa;gDAC/B,OAAM;;;;;;0DAER,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;kDAGrC,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,UAAU;;kEAC/B,8OAAC;wDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,KAAK;;0EAC1B,8OAAC;gEAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;0EAAG,SAAS,KAAK,CAAC,QAAQ;;;;;;0EAC7D,8OAAC;gEAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,UAAU;0EAAG,SAAS,KAAK,CAAC,KAAK;;;;;;;;;;;;kEAE3D,8OAAC;wDAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,aAAa;kEAAG,SAAS,KAAK;;;;;;kEACpD,8OAAC;wDAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,gBAAgB;kEAAG,SAAS,QAAQ;;;;;;;;;;;;0DAG3D,8OAAC;gDAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,mBAAmB;0DACrC,SAAS,WAAW;;;;;;0DAGvB,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;kEAChC,8OAAC;wDAAO,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;;0EACpC,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAI,OAAM;gEAAK,QAAO;gEAAK,SAAQ;gEAAY,MAAK;0EACnD,cAAA,8OAAC;oEAAK,GAAE;oEAA4B,QAAO;oEAAe,aAAY;oEAAI,eAAc;oEAAQ,gBAAe;;;;;;;;;;;;;;;;;kEAGnH,8OAAC;wDAAO,WAAW,+KAAA,CAAA,UAAM,CAAC,eAAe;kEACvC,cAAA,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;;;;;;;;;;;2BA1C/B,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;AAkD9B;uCAEe", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CatalogBanner/catalogBanner.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"backgroundContainer\": \"catalogBanner-module__6ToAlG__backgroundContainer\",\n  \"backgroundImage\": \"catalogBanner-module__6ToAlG__backgroundImage\",\n  \"backgroundOverlay\": \"catalogBanner-module__6ToAlG__backgroundOverlay\",\n  \"buttonRipple\": \"catalogBanner-module__6ToAlG__buttonRipple\",\n  \"catalogBanner\": \"catalogBanner-module__6ToAlG__catalogBanner\",\n  \"catalogStats\": \"catalogBanner-module__6ToAlG__catalogStats\",\n  \"container\": \"catalogBanner-module__6ToAlG__container\",\n  \"content\": \"catalogBanner-module__6ToAlG__content\",\n  \"ctaButton\": \"catalogBanner-module__6ToAlG__ctaButton\",\n  \"ctaContainer\": \"catalogBanner-module__6ToAlG__ctaContainer\",\n  \"ctaIcon\": \"catalogBanner-module__6ToAlG__ctaIcon\",\n  \"ctaText\": \"catalogBanner-module__6ToAlG__ctaText\",\n  \"decorativeCircle\": \"catalogBanner-module__6ToAlG__decorativeCircle\",\n  \"decorativeElements\": \"catalogBanner-module__6ToAlG__decorativeElements\",\n  \"decorativeLine\": \"catalogBanner-module__6ToAlG__decorativeLine\",\n  \"description\": \"catalogBanner-module__6ToAlG__description\",\n  \"feature\": \"catalogBanner-module__6ToAlG__feature\",\n  \"featureIcon\": \"catalogBanner-module__6ToAlG__featureIcon\",\n  \"features\": \"catalogBanner-module__6ToAlG__features\",\n  \"float\": \"catalogBanner-module__6ToAlG__float\",\n  \"slide\": \"catalogBanner-module__6ToAlG__slide\",\n  \"stat\": \"catalogBanner-module__6ToAlG__stat\",\n  \"statLabel\": \"catalogBanner-module__6ToAlG__statLabel\",\n  \"statNumber\": \"catalogBanner-module__6ToAlG__statNumber\",\n  \"subtitle\": \"catalogBanner-module__6ToAlG__subtitle\",\n  \"textContent\": \"catalogBanner-module__6ToAlG__textContent\",\n  \"title\": \"catalogBanner-module__6ToAlG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CatalogBanner/CatalogBanner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport styles from './catalogBanner.module.css';\n\ninterface CatalogBannerProps {\n  title?: string;\n  subtitle?: string;\n  description?: string;\n  ctaText?: string;\n  ctaHref?: string;\n  backgroundImage?: string;\n}\n\nconst CatalogBanner: React.FC<CatalogBannerProps> = ({\n  title = \"Explore Our Complete Catalog\",\n  subtitle = \"Discover Excellence\",\n  description = \"Browse through our comprehensive collection of handcrafted cast stone elements. From architectural details to decorative accents, find the perfect pieces to elevate your space with timeless elegance.\",\n  ctaText = \"View Full Catalog\",\n  ctaHref = \"/catalog\",\n  backgroundImage = \"/images/catalog-banner-bg.jpg\"\n}) => {\n  return (\n    <section className={styles.catalogBanner}>\n      <div className={styles.backgroundContainer}>\n        <Image\n          src={backgroundImage}\n          alt=\"Cast Stone Catalog\"\n          fill\n          className={styles.backgroundImage}\n          sizes=\"100vw\"\n          priority={false}\n        />\n        <div className={styles.backgroundOverlay}></div>\n      </div>\n\n      <div className={styles.container}>\n        <div className={styles.content}>\n          <div className={styles.textContent}>\n            <span className={styles.subtitle}>{subtitle}</span>\n            <h2 className={styles.title}>{title}</h2>\n            <p className={styles.description}>{description}</p>\n            \n            <div className={styles.features}>\n              <div className={styles.feature}>\n                <div className={styles.featureIcon}>\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                </div>\n                <span>500+ Products</span>\n              </div>\n              \n              <div className={styles.feature}>\n                <div className={styles.featureIcon}>\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M9 11L12 14L22 4\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M21 12V19C21 20.1 20.1 21 19 21H5C3.9 21 3 20.1 3 19V5C3.9 5 4.9 5 5 5H16\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                </div>\n                <span>Quality Assured</span>\n              </div>\n              \n              <div className={styles.feature}>\n                <div className={styles.featureIcon}>\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M13 2L3 14H12L11 22L21 10H12L13 2Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                </div>\n                <span>Fast Delivery</span>\n              </div>\n            </div>\n          </div>\n\n          <div className={styles.ctaContainer}>\n            <Link href={ctaHref} className={styles.ctaButton}>\n              <span className={styles.ctaText}>{ctaText}</span>\n              <div className={styles.ctaIcon}>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                </svg>\n              </div>\n              <div className={styles.buttonRipple}></div>\n            </Link>\n            \n            <div className={styles.catalogStats}>\n              <div className={styles.stat}>\n                <span className={styles.statNumber}>25+</span>\n                <span className={styles.statLabel}>Years Experience</span>\n              </div>\n              <div className={styles.stat}>\n                <span className={styles.statNumber}>1000+</span>\n                <span className={styles.statLabel}>Happy Clients</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Decorative Elements */}\n      <div className={styles.decorativeElements}>\n        <div className={styles.decorativeCircle}></div>\n        <div className={styles.decorativeLine}></div>\n      </div>\n    </section>\n  );\n};\n\nexport default CatalogBanner;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAgBA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,8BAA8B,EACtC,WAAW,qBAAqB,EAChC,cAAc,yMAAyM,EACvN,UAAU,mBAAmB,EAC7B,UAAU,UAAU,EACpB,kBAAkB,+BAA+B,EAClD;IACC,qBACE,8OAAC;QAAQ,WAAW,uKAAA,CAAA,UAAM,CAAC,aAAa;;0BACtC,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,mBAAmB;;kCACxC,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAI;wBACJ,IAAI;wBACJ,WAAW,uKAAA,CAAA,UAAM,CAAC,eAAe;wBACjC,OAAM;wBACN,UAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,iBAAiB;;;;;;;;;;;;0BAG1C,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;0BAC9B,cAAA,8OAAC;oBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;sCAC5B,8OAAC;4BAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;8CAChC,8OAAC;oCAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;8CAAG;;;;;;8CACnC,8OAAC;oCAAG,WAAW,uKAAA,CAAA,UAAM,CAAC,KAAK;8CAAG;;;;;;8CAC9B,8OAAC;oCAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;8CAAG;;;;;;8CAEnC,8OAAC;oCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC7B,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;8DAChC,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;;0EACnD,8OAAC;gEAAK,GAAE;gEAA6B,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;0EAChH,8OAAC;gEAAK,GAAE;gEAAoB,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;0EACvG,8OAAC;gEAAK,GAAE;gEAAoB,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;;;;;;;;;;;;8DAG3G,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;8DAChC,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;;0EACnD,8OAAC;gEAAK,GAAE;gEAAmB,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;0EACtG,8OAAC;gEAAK,GAAE;gEAA4E,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;;;;;;;;;;;;8DAGnK,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;8DAChC,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;kEACnD,cAAA,8OAAC;4DAAK,GAAE;4DAAqC,QAAO;4DAAe,aAAY;4DAAI,eAAc;4DAAQ,gBAAe;;;;;;;;;;;;;;;;8DAG5H,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;8CACjC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM;oCAAS,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;;sDAC9C,8OAAC;4CAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;sDAAG;;;;;;sDAClC,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;sDAC5B,cAAA,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,MAAK;0DACnD,cAAA,8OAAC;oDAAK,GAAE;oDAA4B,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;;;;;;;;;;;;;;;;sDAGnH,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;8CAGrC,8OAAC;oCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;sDACjC,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,IAAI;;8DACzB,8OAAC;oDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;8DAAE;;;;;;8DACpC,8OAAC;oDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;;;;;;;sDAErC,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,IAAI;;8DACzB,8OAAC;oDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;8DAAE;;;;;;8DACpC,8OAAC;oDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,kBAAkB;;kCACvC,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,gBAAgB;;;;;;kCACvC,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;AAI7C;uCAEe", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CollectionsCarousel/collectionsCarousel.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionText\": \"collectionsCarousel-module____MCqq__actionText\",\n  \"cardAction\": \"collectionsCarousel-module____MCqq__cardAction\",\n  \"cardContent\": \"collectionsCarousel-module____MCqq__cardContent\",\n  \"cardLink\": \"collectionsCarousel-module____MCqq__cardLink\",\n  \"carousel\": \"collectionsCarousel-module____MCqq__carousel\",\n  \"carouselContainer\": \"collectionsCarousel-module____MCqq__carouselContainer\",\n  \"collectionCard\": \"collectionsCarousel-module____MCqq__collectionCard\",\n  \"collectionDescription\": \"collectionsCarousel-module____MCqq__collectionDescription\",\n  \"collectionImage\": \"collectionsCarousel-module____MCqq__collectionImage\",\n  \"collectionName\": \"collectionsCarousel-module____MCqq__collectionName\",\n  \"collectionsSection\": \"collectionsCarousel-module____MCqq__collectionsSection\",\n  \"container\": \"collectionsCarousel-module____MCqq__container\",\n  \"errorContainer\": \"collectionsCarousel-module____MCqq__errorContainer\",\n  \"header\": \"collectionsCarousel-module____MCqq__header\",\n  \"headerContent\": \"collectionsCarousel-module____MCqq__headerContent\",\n  \"imageContainer\": \"collectionsCarousel-module____MCqq__imageContainer\",\n  \"imageOverlay\": \"collectionsCarousel-module____MCqq__imageOverlay\",\n  \"loadingContainer\": \"collectionsCarousel-module____MCqq__loadingContainer\",\n  \"loadingSpinner\": \"collectionsCarousel-module____MCqq__loadingSpinner\",\n  \"navButton\": \"collectionsCarousel-module____MCqq__navButton\",\n  \"navigation\": \"collectionsCarousel-module____MCqq__navigation\",\n  \"productCount\": \"collectionsCarousel-module____MCqq__productCount\",\n  \"spin\": \"collectionsCarousel-module____MCqq__spin\",\n  \"subtitle\": \"collectionsCarousel-module____MCqq__subtitle\",\n  \"title\": \"collectionsCarousel-module____MCqq__title\",\n  \"viewAllButton\": \"collectionsCarousel-module____MCqq__viewAllButton\",\n  \"viewAllContainer\": \"collectionsCarousel-module____MCqq__viewAllContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CollectionsCarousel/CollectionsCarousel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { collectionGetService } from '../../../services/api/collections';\nimport { CollectionHierarchy } from '../../../services/types/entities';\nimport styles from './collectionsCarousel.module.css';\n\ninterface CollectionsCarouselProps {\n  title?: string;\n  subtitle?: string;\n}\n\nconst CollectionsCarousel: React.FC<CollectionsCarouselProps> = ({\n  title = \"Featured Collections\",\n  subtitle = \"Explore our curated selection of handcrafted cast stone collections\"\n}) => {\n  const [collections, setCollections] = useState<CollectionHierarchy[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const fetchCollections = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const hierarchyData = await collectionGetService.getHierarchy();\n        setCollections(hierarchyData);\n      } catch (err) {\n        console.error('Failed to fetch collections:', err);\n        setError('Failed to load collections');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchCollections();\n  }, []);\n\n  const scrollLeft = () => {\n    if (scrollContainerRef.current) {\n      scrollContainerRef.current.scrollBy({\n        left: -300,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  const scrollRight = () => {\n    if (scrollContainerRef.current) {\n      scrollContainerRef.current.scrollBy({\n        left: 300,\n        behavior: 'smooth'\n      });\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <section className={styles.collectionsSection}>\n        <div className={styles.container}>\n          <div className={styles.header}>\n            <h2 className={styles.title}>{title}</h2>\n            <p className={styles.subtitle}>{subtitle}</p>\n          </div>\n          <div className={styles.loadingContainer}>\n            <div className={styles.loadingSpinner}></div>\n            <p>Loading collections...</p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error) {\n    return (\n      <section className={styles.collectionsSection}>\n        <div className={styles.container}>\n          <div className={styles.header}>\n            <h2 className={styles.title}>{title}</h2>\n            <p className={styles.subtitle}>{subtitle}</p>\n          </div>\n          <div className={styles.errorContainer}>\n            <p>Unable to load collections. Please try again later.</p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className={styles.collectionsSection}>\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <div className={styles.headerContent}>\n            <h2 className={styles.title}>{title}</h2>\n            <p className={styles.subtitle}>{subtitle}</p>\n          </div>\n          \n          <div className={styles.navigation}>\n            <button \n              className={styles.navButton} \n              onClick={scrollLeft}\n              aria-label=\"Scroll left\"\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M15 18L9 12L15 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              </svg>\n            </button>\n            <button \n              className={styles.navButton} \n              onClick={scrollRight}\n              aria-label=\"Scroll right\"\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M9 18L15 12L9 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <div className={styles.carouselContainer}>\n          <div \n            ref={scrollContainerRef}\n            className={styles.carousel}\n          >\n            {collections.map((collection) => (\n              <div key={collection.id} className={styles.collectionCard}>\n                <Link href={`/collections/${collection.id}`} className={styles.cardLink}>\n                  <div className={styles.imageContainer}>\n                    <Image\n                      src={`/images/collections/${collection.id}.jpg`}\n                      alt={collection.name}\n                      fill\n                      className={styles.collectionImage}\n                      sizes=\"(max-width: 768px) 280px, 320px\"\n                      onError={(e) => {\n                        // Fallback to placeholder image\n                        const target = e.target as HTMLImageElement;\n                        target.src = '/images/placeholder-collection.jpg';\n                      }}\n                    />\n                    <div className={styles.imageOverlay}></div>\n                    \n                    <div className={styles.cardContent}>\n                      <div className={styles.productCount}>\n                        {collection.productCount} Products\n                      </div>\n                      <h3 className={styles.collectionName}>{collection.name}</h3>\n                      {collection.description && (\n                        <p className={styles.collectionDescription}>\n                          {collection.description}\n                        </p>\n                      )}\n                      \n                      <div className={styles.cardAction}>\n                        <span className={styles.actionText}>Explore Collection</span>\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        </svg>\n                      </div>\n                    </div>\n                  </div>\n                </Link>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className={styles.viewAllContainer}>\n          <Link href=\"/collections\" className={styles.viewAllButton}>\n            <span>View All Collections</span>\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n              <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default CollectionsCarousel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAPA;;;;;;;AAcA,MAAM,sBAA0D,CAAC,EAC/D,QAAQ,sBAAsB,EAC9B,WAAW,qEAAqE,EACjF;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,aAAa;gBACb,SAAS;gBACT,MAAM,gBAAgB,MAAM,4IAAA,CAAA,uBAAoB,CAAC,YAAY;gBAC7D,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAClC,MAAM,CAAC;gBACP,UAAU;YACZ;QACF;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAClC,MAAM;gBACN,UAAU;YACZ;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;sBAC3C,cAAA,8OAAC;gBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,8OAAC;wBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;;0CAC3B,8OAAC;gCAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;0CAAG;;;;;;0CAC9B,8OAAC;gCAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG;;;;;;;;;;;;kCAElC,8OAAC;wBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,gBAAgB;;0CACrC,8OAAC;gCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;;;;;;0CACrC,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;sBAC3C,cAAA,8OAAC;gBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,8OAAC;wBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;;0CAC3B,8OAAC;gCAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;0CAAG;;;;;;0CAC9B,8OAAC;gCAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG;;;;;;;;;;;;kCAElC,8OAAC;wBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC;QAAQ,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;kBAC3C,cAAA,8OAAC;YAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;;sCAC3B,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,aAAa;;8CAClC,8OAAC;oCAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;8CAAG;;;;;;8CAC9B,8OAAC;oCAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;8CAAG;;;;;;;;;;;;sCAGlC,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;;8CAC/B,8OAAC;oCACC,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;oCAC3B,SAAS;oCACT,cAAW;8CAEX,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;kDACnD,cAAA,8OAAC;4CAAK,GAAE;4CAAmB,QAAO;4CAAe,aAAY;4CAAI,eAAc;4CAAQ,gBAAe;;;;;;;;;;;;;;;;8CAG1G,8OAAC;oCACC,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;oCAC3B,SAAS;oCACT,cAAW;8CAEX,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;kDACnD,cAAA,8OAAC;4CAAK,GAAE;4CAAkB,QAAO;4CAAe,aAAY;4CAAI,eAAc;4CAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM7G,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,iBAAiB;8BACtC,cAAA,8OAAC;wBACC,KAAK;wBACL,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;kCAEzB,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;gCAAwB,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;0CACvD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;oCAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;8CACrE,cAAA,8OAAC;wCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;;0DACnC,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,CAAC,oBAAoB,EAAE,WAAW,EAAE,CAAC,IAAI,CAAC;gDAC/C,KAAK,WAAW,IAAI;gDACpB,IAAI;gDACJ,WAAW,mLAAA,CAAA,UAAM,CAAC,eAAe;gDACjC,OAAM;gDACN,SAAS,CAAC;oDACR,gCAAgC;oDAChC,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,GAAG,GAAG;gDACf;;;;;;0DAEF,8OAAC;gDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,YAAY;;;;;;0DAEnC,8OAAC;gDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;;kEAChC,8OAAC;wDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,YAAY;;4DAChC,WAAW,YAAY;4DAAC;;;;;;;kEAE3B,8OAAC;wDAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;kEAAG,WAAW,IAAI;;;;;;oDACrD,WAAW,WAAW,kBACrB,8OAAC;wDAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,qBAAqB;kEACvC,WAAW,WAAW;;;;;;kEAI3B,8OAAC;wDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;;0EAC/B,8OAAC;gEAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;0EAAE;;;;;;0EACpC,8OAAC;gEAAI,OAAM;gEAAK,QAAO;gEAAK,SAAQ;gEAAY,MAAK;0EACnD,cAAA,8OAAC;oEAAK,GAAE;oEAA4B,QAAO;oEAAe,aAAY;oEAAI,eAAc;oEAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA/BjH,WAAW,EAAE;;;;;;;;;;;;;;;8BA0C7B,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,gBAAgB;8BACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAe,WAAW,mLAAA,CAAA,UAAM,CAAC,aAAa;;0CACvD,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;0CACnD,cAAA,8OAAC;oCAAK,GAAE;oCAA4B,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7H;uCAEe", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/TestimonialsSection/testimonialsSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"testimonialsSection-module__2JZFAW__active\",\n  \"authorCompany\": \"testimonialsSection-module__2JZFAW__authorCompany\",\n  \"authorDetails\": \"testimonialsSection-module__2JZFAW__authorDetails\",\n  \"authorImage\": \"testimonialsSection-module__2JZFAW__authorImage\",\n  \"authorInfo\": \"testimonialsSection-module__2JZFAW__authorInfo\",\n  \"authorName\": \"testimonialsSection-module__2JZFAW__authorName\",\n  \"authorPhoto\": \"testimonialsSection-module__2JZFAW__authorPhoto\",\n  \"authorTitle\": \"testimonialsSection-module__2JZFAW__authorTitle\",\n  \"container\": \"testimonialsSection-module__2JZFAW__container\",\n  \"description\": \"testimonialsSection-module__2JZFAW__description\",\n  \"header\": \"testimonialsSection-module__2JZFAW__header\",\n  \"navDot\": \"testimonialsSection-module__2JZFAW__navDot\",\n  \"navigation\": \"testimonialsSection-module__2JZFAW__navigation\",\n  \"projectInfo\": \"testimonialsSection-module__2JZFAW__projectInfo\",\n  \"projectLabel\": \"testimonialsSection-module__2JZFAW__projectLabel\",\n  \"projectName\": \"testimonialsSection-module__2JZFAW__projectName\",\n  \"quoteIcon\": \"testimonialsSection-module__2JZFAW__quoteIcon\",\n  \"rating\": \"testimonialsSection-module__2JZFAW__rating\",\n  \"stat\": \"testimonialsSection-module__2JZFAW__stat\",\n  \"statLabel\": \"testimonialsSection-module__2JZFAW__statLabel\",\n  \"statNumber\": \"testimonialsSection-module__2JZFAW__statNumber\",\n  \"stats\": \"testimonialsSection-module__2JZFAW__stats\",\n  \"subtitle\": \"testimonialsSection-module__2JZFAW__subtitle\",\n  \"testimonialContent\": \"testimonialsSection-module__2JZFAW__testimonialContent\",\n  \"testimonialMeta\": \"testimonialsSection-module__2JZFAW__testimonialMeta\",\n  \"testimonialText\": \"testimonialsSection-module__2JZFAW__testimonialText\",\n  \"testimonialsContainer\": \"testimonialsSection-module__2JZFAW__testimonialsContainer\",\n  \"testimonialsSection\": \"testimonialsSection-module__2JZFAW__testimonialsSection\",\n  \"title\": \"testimonialsSection-module__2JZFAW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/TestimonialsSection/TestimonialsSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport styles from './testimonialsSection.module.css';\n\ninterface Testimonial {\n  id: string;\n  name: string;\n  title: string;\n  company: string;\n  content: string;\n  image: string;\n  rating: number;\n  project: string;\n}\n\ninterface TestimonialsSectionProps {\n  testimonials?: Testimonial[];\n}\n\nconst defaultTestimonials: Testimonial[] = [\n  {\n    id: '1',\n    name: '<PERSON>',\n    title: 'Interior Designer',\n    company: 'Mitchell Design Studio',\n    content: 'Cast Stone has transformed our projects with their exquisite craftsmanship. The attention to detail and quality of their architectural elements is unmatched. Our clients are consistently amazed by the timeless elegance these pieces bring to their spaces.',\n    image: '/images/testimonials/sarah-mitchell.jpg',\n    rating: 5,\n    project: 'Luxury Residential Project'\n  },\n  {\n    id: '2',\n    name: '<PERSON>',\n    title: 'Architect',\n    company: 'Chen Architecture Group',\n    content: 'Working with Cast Stone has been a game-changer for our firm. Their ability to create custom pieces that perfectly match our architectural vision is remarkable. The durability and beauty of their cast stone elements make them our go-to choice for premium projects.',\n    image: '/images/testimonials/michael-chen.jpg',\n    rating: 5,\n    project: 'Commercial Heritage Building'\n  },\n  {\n    id: '3',\n    name: '<PERSON>',\n    title: 'Project Manager',\n    company: 'Elite Construction',\n    content: 'The professionalism and expertise of the Cast Stone team is exceptional. From initial consultation to final installation, every step was handled with precision. The end result exceeded our expectations and our client\\'s vision was brought to life beautifully.',\n    image: '/images/testimonials/emma-rodriguez.jpg',\n    rating: 5,\n    project: 'Boutique Hotel Renovation'\n  }\n];\n\nconst TestimonialsSection: React.FC<TestimonialsSectionProps> = ({\n  testimonials = defaultTestimonials\n}) => {\n  const [activeTestimonial, setActiveTestimonial] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  useEffect(() => {\n    if (!isAutoPlaying) return;\n\n    const interval = setInterval(() => {\n      setActiveTestimonial((prev) => \n        prev === testimonials.length - 1 ? 0 : prev + 1\n      );\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, testimonials.length]);\n\n  const handleTestimonialChange = (index: number) => {\n    setActiveTestimonial(index);\n    setIsAutoPlaying(false);\n    \n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  };\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, index) => (\n      <svg\n        key={index}\n        width=\"16\"\n        height=\"16\"\n        viewBox=\"0 0 24 24\"\n        fill={index < rating ? \"#d4af8c\" : \"none\"}\n        stroke={index < rating ? \"#d4af8c\" : \"#e5e5e5\"}\n        strokeWidth=\"1\"\n      >\n        <polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\" />\n      </svg>\n    ));\n  };\n\n  return (\n    <section className={styles.testimonialsSection}>\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <span className={styles.subtitle}>Client Testimonials</span>\n          <h2 className={styles.title}>What Our Clients Say</h2>\n          <p className={styles.description}>\n            Discover why architects, designers, and homeowners trust Cast Stone \n            for their most important projects.\n          </p>\n        </div>\n\n        <div className={styles.testimonialsContainer}>\n          <div className={styles.testimonialContent}>\n            <div className={styles.quoteIcon}>\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 21C3 17.5 5.5 15 9 15C9.5 15 10 15.1 10.5 15.2C10.2 14.2 10 13.1 10 12C10 8.7 12.7 6 16 6V4C11.6 4 8 7.6 8 12C8 12.4 8 12.7 8.1 13.1C6.8 13.6 5.7 14.4 4.9 15.5C3.8 17.1 3 19 3 21Z\" fill=\"currentColor\"/>\n                <path d=\"M13 21C13 17.5 15.5 15 19 15C19.5 15 20 15.1 20.5 15.2C20.2 14.2 20 13.1 20 12C20 8.7 22.7 6 26 6V4C21.6 4 18 7.6 18 12C18 12.4 18 12.7 18.1 13.1C16.8 13.6 15.7 14.4 14.9 15.5C13.8 17.1 13 19 13 21Z\" fill=\"currentColor\"/>\n              </svg>\n            </div>\n\n            <div className={styles.testimonialText}>\n              <p className={styles.testimonialContent}>\n                \"{testimonials[activeTestimonial].content}\"\n              </p>\n              \n              <div className={styles.rating}>\n                {renderStars(testimonials[activeTestimonial].rating)}\n              </div>\n              \n              <div className={styles.projectInfo}>\n                <span className={styles.projectLabel}>Project:</span>\n                <span className={styles.projectName}>\n                  {testimonials[activeTestimonial].project}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div className={styles.testimonialMeta}>\n            <div className={styles.authorInfo}>\n              <div className={styles.authorImage}>\n                <Image\n                  src={testimonials[activeTestimonial].image}\n                  alt={testimonials[activeTestimonial].name}\n                  fill\n                  className={styles.authorPhoto}\n                  sizes=\"80px\"\n                  onError={(e) => {\n                    const target = e.target as HTMLImageElement;\n                    target.src = '/images/placeholder-avatar.jpg';\n                  }}\n                />\n              </div>\n              \n              <div className={styles.authorDetails}>\n                <h4 className={styles.authorName}>\n                  {testimonials[activeTestimonial].name}\n                </h4>\n                <p className={styles.authorTitle}>\n                  {testimonials[activeTestimonial].title}\n                </p>\n                <p className={styles.authorCompany}>\n                  {testimonials[activeTestimonial].company}\n                </p>\n              </div>\n            </div>\n\n            <div className={styles.navigation}>\n              {testimonials.map((_, index) => (\n                <button\n                  key={index}\n                  className={`${styles.navDot} ${\n                    index === activeTestimonial ? styles.active : ''\n                  }`}\n                  onClick={() => handleTestimonialChange(index)}\n                  aria-label={`View testimonial ${index + 1}`}\n                />\n              ))}\n            </div>\n          </div>\n        </div>\n\n        <div className={styles.stats}>\n          <div className={styles.stat}>\n            <span className={styles.statNumber}>25+</span>\n            <span className={styles.statLabel}>Years Experience</span>\n          </div>\n          <div className={styles.stat}>\n            <span className={styles.statNumber}>1000+</span>\n            <span className={styles.statLabel}>Projects Completed</span>\n          </div>\n          <div className={styles.stat}>\n            <span className={styles.statNumber}>98%</span>\n            <span className={styles.statLabel}>Client Satisfaction</span>\n          </div>\n          <div className={styles.stat}>\n            <span className={styles.statNumber}>50+</span>\n            <span className={styles.statLabel}>Awards Won</span>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TestimonialsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAqBA,MAAM,sBAAqC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;CACD;AAED,MAAM,sBAA0D,CAAC,EAC/D,eAAe,mBAAmB,EACnC;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,WAAW,YAAY;YAC3B,qBAAqB,CAAC,OACpB,SAAS,aAAa,MAAM,GAAG,IAAI,IAAI,OAAO;QAElD,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe,aAAa,MAAM;KAAC;IAEvC,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB;QACrB,iBAAiB;QAEjB,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,sBACnC,8OAAC;gBAEC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAM,QAAQ,SAAS,YAAY;gBACnC,QAAQ,QAAQ,SAAS,YAAY;gBACrC,aAAY;0BAEZ,cAAA,8OAAC;oBAAQ,QAAO;;;;;;eARX;;;;;IAWX;IAEA,qBACE,8OAAC;QAAQ,WAAW,mLAAA,CAAA,UAAM,CAAC,mBAAmB;kBAC5C,cAAA,8OAAC;YAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;;sCAC3B,8OAAC;4BAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;sCAAE;;;;;;sCAClC,8OAAC;4BAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;sCAC7B,8OAAC;4BAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;sCAAE;;;;;;;;;;;;8BAMpC,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,qBAAqB;;sCAC1C,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;;8CACvC,8OAAC;oCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAC9B,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;;0DACnD,8OAAC;gDAAK,GAAE;gDAAyL,MAAK;;;;;;0DACtM,8OAAC;gDAAK,GAAE;gDAAyM,MAAK;;;;;;;;;;;;;;;;;8CAI1N,8OAAC;oCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,eAAe;;sDACpC,8OAAC;4CAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;;gDAAE;gDACrC,YAAY,CAAC,kBAAkB,CAAC,OAAO;gDAAC;;;;;;;sDAG5C,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;sDAC1B,YAAY,YAAY,CAAC,kBAAkB,CAAC,MAAM;;;;;;sDAGrD,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;;8DAChC,8OAAC;oDAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,YAAY;8DAAE;;;;;;8DACtC,8OAAC;oDAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;8DAChC,YAAY,CAAC,kBAAkB,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,eAAe;;8CACpC,8OAAC;oCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;;sDAC/B,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;sDAChC,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,YAAY,CAAC,kBAAkB,CAAC,KAAK;gDAC1C,KAAK,YAAY,CAAC,kBAAkB,CAAC,IAAI;gDACzC,IAAI;gDACJ,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,OAAM;gDACN,SAAS,CAAC;oDACR,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,GAAG,GAAG;gDACf;;;;;;;;;;;sDAIJ,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,aAAa;;8DAClC,8OAAC;oDAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8DAC7B,YAAY,CAAC,kBAAkB,CAAC,IAAI;;;;;;8DAEvC,8OAAC;oDAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;8DAC7B,YAAY,CAAC,kBAAkB,CAAC,KAAK;;;;;;8DAExC,8OAAC;oDAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,aAAa;8DAC/B,YAAY,CAAC,kBAAkB,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAK9C,8OAAC;oCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAC9B,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;4CAEC,WAAW,GAAG,mLAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,EAC3B,UAAU,oBAAoB,mLAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC9C;4CACF,SAAS,IAAM,wBAAwB;4CACvC,cAAY,CAAC,iBAAiB,EAAE,QAAQ,GAAG;2CALtC;;;;;;;;;;;;;;;;;;;;;;8BAYf,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;;sCAC1B,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;sCAErC,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;sCAErC,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;sCAErC,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/C;uCAEe", "debugId": null}}, {"offset": {"line": 2072, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/homeComponent.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"homeComponent\": \"homeComponent-module__wtPNwW__homeComponent\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HomeComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport HeroSection from './HeroSection/HeroSection';\nimport CategoriesSection from './CategoriesSection/CategoriesSection';\nimport CatalogBanner from './CatalogBanner/CatalogBanner';\nimport CollectionsCarousel from './CollectionsCarousel/CollectionsCarousel';\nimport TestimonialsSection from './TestimonialsSection/TestimonialsSection';\nimport styles from './homeComponent.module.css';\n\ninterface HomeComponentProps {\n  title?: string;\n  subtitle?: string;\n}\n\nconst HomeComponent: React.FC<HomeComponentProps> = ({\n  title = \"Timeless Elegance in Cast Stone\",\n  subtitle = \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\"\n}) => {\n  return (\n    <main className={styles.homeComponent}>\n      {/* Hero Section with Image Carousel */}\n      <HeroSection\n        title={title}\n        subtitle={subtitle}\n      />\n\n      {/* Categories Grid Section */}\n      <CategoriesSection />\n\n      {/* Catalog Banner */}\n      <CatalogBanner />\n\n      {/* Collections Carousel */}\n      <CollectionsCarousel />\n\n      {/* Testimonials Section */}\n      <TestimonialsSection />\n    </main>\n  );\n};\n\nexport default HomeComponent;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAeA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,iCAAiC,EACzC,WAAW,qJAAqJ,EACjK;IACC,qBACE,8OAAC;QAAK,WAAW,sJAAA,CAAA,UAAM,CAAC,aAAa;;0BAEnC,8OAAC,wJAAA,CAAA,UAAW;gBACV,OAAO;gBACP,UAAU;;;;;;0BAIZ,8OAAC,oKAAA,CAAA,UAAiB;;;;;0BAGlB,8OAAC,4JAAA,CAAA,UAAa;;;;;0BAGd,8OAAC,wKAAA,CAAA,UAAmB;;;;;0BAGpB,8OAAC,wKAAA,CAAA,UAAmB;;;;;;;;;;;AAG1B;uCAEe", "debugId": null}}]}