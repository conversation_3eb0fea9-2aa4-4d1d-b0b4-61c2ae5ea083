{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartItem/cartItem.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cartItem\": \"cartItem-module__IXYF0W__cartItem\",\n  \"collection\": \"cartItem-module__IXYF0W__collection\",\n  \"imageContainer\": \"cartItem-module__IXYF0W__imageContainer\",\n  \"inStock\": \"cartItem-module__IXYF0W__inStock\",\n  \"itemTotal\": \"cartItem-module__IXYF0W__itemTotal\",\n  \"outOfStock\": \"cartItem-module__IXYF0W__outOfStock\",\n  \"priceBreakdown\": \"cartItem-module__IXYF0W__priceBreakdown\",\n  \"priceSection\": \"cartItem-module__IXYF0W__priceSection\",\n  \"productDescription\": \"cartItem-module__IXYF0W__productDescription\",\n  \"productDetails\": \"cartItem-module__IXYF0W__productDetails\",\n  \"productImage\": \"cartItem-module__IXYF0W__productImage\",\n  \"productMeta\": \"cartItem-module__IXYF0W__productMeta\",\n  \"productName\": \"cartItem-module__IXYF0W__productName\",\n  \"quantity\": \"cartItem-module__IXYF0W__quantity\",\n  \"quantityBtn\": \"cartItem-module__IXYF0W__quantityBtn\",\n  \"quantityControls\": \"cartItem-module__IXYF0W__quantityControls\",\n  \"quantityLabel\": \"cartItem-module__IXYF0W__quantityLabel\",\n  \"quantitySection\": \"cartItem-module__IXYF0W__quantitySection\",\n  \"removeBtn\": \"cartItem-module__IXYF0W__removeBtn\",\n  \"removeSection\": \"cartItem-module__IXYF0W__removeSection\",\n  \"removing\": \"cartItem-module__IXYF0W__removing\",\n  \"stockStatus\": \"cartItem-module__IXYF0W__stockStatus\",\n  \"unitPrice\": \"cartItem-module__IXYF0W__unitPrice\",\n  \"updating\": \"cartItem-module__IXYF0W__updating\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartItem/CartItem.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { CartItem as CartItemType } from '@/services/types/entities';\nimport { useCart } from '@/contexts/CartContext';\nimport styles from './cartItem.module.css';\n\ninterface CartItemProps {\n  item: CartItemType;\n}\n\nconst CartItem: React.FC<CartItemProps> = ({ item }) => {\n  const { updateCartItem, removeFromCart, state } = useCart();\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [isRemoving, setIsRemoving] = useState(false);\n\n  const handleQuantityChange = async (newQuantity: number) => {\n    if (newQuantity < 1 || !item.product) return;\n    \n    if (newQuantity > item.product.stock) {\n      alert(`Only ${item.product.stock} items available in stock`);\n      return;\n    }\n\n    try {\n      setIsUpdating(true);\n      await updateCartItem(item.productId, newQuantity);\n    } catch (error) {\n      console.error('Error updating quantity:', error);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const handleRemove = async () => {\n    try {\n      setIsRemoving(true);\n      await removeFromCart(item.productId);\n    } catch (error) {\n      console.error('Error removing item:', error);\n    } finally {\n      setIsRemoving(false);\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(price);\n  };\n\n  if (!item.product) {\n    return null;\n  }\n\n  const mainImage = item.product.images && item.product.images.length > 0 \n    ? item.product.images[0] \n    : '/images/placeholder-product.jpg';\n\n  const itemTotal = item.quantity * item.product.price;\n\n  return (\n    <div className={styles.cartItem}>\n      {/* Product Image */}\n      <div className={styles.imageContainer}>\n        <img\n          src={mainImage}\n          alt={item.product.name}\n          className={styles.productImage}\n        />\n      </div>\n\n      {/* Product Details */}\n      <div className={styles.productDetails}>\n        <h3 className={styles.productName}>{item.product.name}</h3>\n        \n        {item.product.description && (\n          <p className={styles.productDescription}>\n            {item.product.description.length > 150 \n              ? `${item.product.description.substring(0, 150)}...` \n              : item.product.description}\n          </p>\n        )}\n\n        <div className={styles.productMeta}>\n          <span className={styles.unitPrice}>\n            {formatPrice(item.product.price)} each\n          </span>\n          {item.product.collection && (\n            <span className={styles.collection}>\n              {item.product.collection.name}\n            </span>\n          )}\n        </div>\n\n        {/* Stock Status */}\n        <div className={styles.stockStatus}>\n          {item.product.stock > 0 ? (\n            <span className={styles.inStock}>\n              {item.product.stock > 10 ? 'In Stock' : `Only ${item.product.stock} left`}\n            </span>\n          ) : (\n            <span className={styles.outOfStock}>Out of Stock</span>\n          )}\n        </div>\n      </div>\n\n      {/* Quantity Controls */}\n      <div className={styles.quantitySection}>\n        <label className={styles.quantityLabel}>Quantity</label>\n        <div className={styles.quantityControls}>\n          <button\n            type=\"button\"\n            onClick={() => handleQuantityChange(item.quantity - 1)}\n            disabled={item.quantity <= 1 || isUpdating || state.isLoading}\n            className={styles.quantityBtn}\n          >\n            -\n          </button>\n          <span className={styles.quantity}>{item.quantity}</span>\n          <button\n            type=\"button\"\n            onClick={() => handleQuantityChange(item.quantity + 1)}\n            disabled={item.quantity >= item.product.stock || isUpdating || state.isLoading}\n            className={styles.quantityBtn}\n          >\n            +\n          </button>\n        </div>\n        {isUpdating && (\n          <span className={styles.updating}>Updating...</span>\n        )}\n      </div>\n\n      {/* Price Section */}\n      <div className={styles.priceSection}>\n        <div className={styles.itemTotal}>\n          {formatPrice(itemTotal)}\n        </div>\n        <div className={styles.priceBreakdown}>\n          {item.quantity} × {formatPrice(item.product.price)}\n        </div>\n      </div>\n\n      {/* Remove Button */}\n      <div className={styles.removeSection}>\n        <button\n          onClick={handleRemove}\n          disabled={isRemoving || state.isLoading}\n          className={styles.removeBtn}\n          title=\"Remove from cart\"\n        >\n          {isRemoving ? (\n            <span className={styles.removing}>...</span>\n          ) : (\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M3 6h18\"/>\n              <path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/>\n              <path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"/>\n              <line x1=\"10\" y1=\"11\" x2=\"10\" y2=\"17\"/>\n              <line x1=\"14\" y1=\"11\" x2=\"14\" y2=\"17\"/>\n            </svg>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CartItem;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAWA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE;IACjD,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,uBAAuB,OAAO;QAClC,IAAI,cAAc,KAAK,CAAC,KAAK,OAAO,EAAE;QAEtC,IAAI,cAAc,KAAK,OAAO,CAAC,KAAK,EAAE;YACpC,MAAM,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC;YAC3D;QACF;QAEA,IAAI;YACF,cAAc;YACd,MAAM,eAAe,KAAK,SAAS,EAAE;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,cAAc;YACd,MAAM,eAAe,KAAK,SAAS;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,IAAI,CAAC,KAAK,OAAO,EAAE;QACjB,OAAO;IACT;IAEA,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,IAAI,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,IAClE,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,GACtB;IAEJ,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,OAAO,CAAC,KAAK;IAEpD,qBACE,8OAAC;QAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,QAAQ;;0BAE7B,8OAAC;gBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,cAAc;0BACnC,cAAA,8OAAC;oBACC,KAAK;oBACL,KAAK,KAAK,OAAO,CAAC,IAAI;oBACtB,WAAW,6JAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;0BAKlC,8OAAC;gBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,8OAAC;wBAAG,WAAW,6JAAA,CAAA,UAAM,CAAC,WAAW;kCAAG,KAAK,OAAO,CAAC,IAAI;;;;;;oBAEpD,KAAK,OAAO,CAAC,WAAW,kBACvB,8OAAC;wBAAE,WAAW,6JAAA,CAAA,UAAM,CAAC,kBAAkB;kCACpC,KAAK,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,MAC/B,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAClD,KAAK,OAAO,CAAC,WAAW;;;;;;kCAIhC,8OAAC;wBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;gCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;;oCAC9B,YAAY,KAAK,OAAO,CAAC,KAAK;oCAAE;;;;;;;4BAElC,KAAK,OAAO,CAAC,UAAU,kBACtB,8OAAC;gCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,KAAK,OAAO,CAAC,UAAU,CAAC,IAAI;;;;;;;;;;;;kCAMnC,8OAAC;wBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,WAAW;kCAC/B,KAAK,OAAO,CAAC,KAAK,GAAG,kBACpB,8OAAC;4BAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,OAAO;sCAC5B,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;;;;;iDAG3E,8OAAC;4BAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,UAAU;sCAAE;;;;;;;;;;;;;;;;;0BAM1C,8OAAC;gBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,eAAe;;kCACpC,8OAAC;wBAAM,WAAW,6JAAA,CAAA,UAAM,CAAC,aAAa;kCAAE;;;;;;kCACxC,8OAAC;wBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,gBAAgB;;0CACrC,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,qBAAqB,KAAK,QAAQ,GAAG;gCACpD,UAAU,KAAK,QAAQ,IAAI,KAAK,cAAc,MAAM,SAAS;gCAC7D,WAAW,6JAAA,CAAA,UAAM,CAAC,WAAW;0CAC9B;;;;;;0CAGD,8OAAC;gCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG,KAAK,QAAQ;;;;;;0CAChD,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,qBAAqB,KAAK,QAAQ,GAAG;gCACpD,UAAU,KAAK,QAAQ,IAAI,KAAK,OAAO,CAAC,KAAK,IAAI,cAAc,MAAM,SAAS;gCAC9E,WAAW,6JAAA,CAAA,UAAM,CAAC,WAAW;0CAC9B;;;;;;;;;;;;oBAIF,4BACC,8OAAC;wBAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAKtC,8OAAC;gBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,8OAAC;wBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;kCAC7B,YAAY;;;;;;kCAEf,8OAAC;wBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,cAAc;;4BAClC,KAAK,QAAQ;4BAAC;4BAAI,YAAY,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;;0BAKrD,8OAAC;gBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,aAAa;0BAClC,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,cAAc,MAAM,SAAS;oBACvC,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;oBAC3B,OAAM;8BAEL,2BACC,8OAAC;wBAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;6CAElC,8OAAC;wBAAI,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1C,8OAAC;gCAAK,GAAE;;;;;;0CACR,8OAAC;gCAAK,GAAE;;;;;;0CACR,8OAAC;gCAAK,GAAE;;;;;;0CACR,8OAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;;;;;;0CACjC,8OAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;uCAEe", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartSummary/cartSummary.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionButtons\": \"cartSummary-module__kqlNYa__actionButtons\",\n  \"cartSummary\": \"cartSummary-module__kqlNYa__cartSummary\",\n  \"checkoutBtn\": \"cartSummary-module__kqlNYa__checkoutBtn\",\n  \"checkoutIcon\": \"cartSummary-module__kqlNYa__checkoutIcon\",\n  \"clearBtn\": \"cartSummary-module__kqlNYa__clearBtn\",\n  \"clearIcon\": \"cartSummary-module__kqlNYa__clearIcon\",\n  \"divider\": \"cartSummary-module__kqlNYa__divider\",\n  \"freeShipping\": \"cartSummary-module__kqlNYa__freeShipping\",\n  \"infoIcon\": \"cartSummary-module__kqlNYa__infoIcon\",\n  \"label\": \"cartSummary-module__kqlNYa__label\",\n  \"securityIcon\": \"cartSummary-module__kqlNYa__securityIcon\",\n  \"securityNotice\": \"cartSummary-module__kqlNYa__securityNotice\",\n  \"shippingNotice\": \"cartSummary-module__kqlNYa__shippingNotice\",\n  \"summaryDetails\": \"cartSummary-module__kqlNYa__summaryDetails\",\n  \"summaryRow\": \"cartSummary-module__kqlNYa__summaryRow\",\n  \"title\": \"cartSummary-module__kqlNYa__title\",\n  \"totalLabel\": \"cartSummary-module__kqlNYa__totalLabel\",\n  \"totalRow\": \"cartSummary-module__kqlNYa__totalRow\",\n  \"totalValue\": \"cartSummary-module__kqlNYa__totalValue\",\n  \"value\": \"cartSummary-module__kqlNYa__value\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartSummary/CartSummary.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useCart } from '@/contexts/CartContext';\nimport styles from './cartSummary.module.css';\n\ninterface CartSummaryProps {\n  showCheckoutButton?: boolean;\n  showClearButton?: boolean;\n}\n\nconst CartSummary: React.FC<CartSummaryProps> = ({\n  showCheckoutButton = true,\n  showClearButton = true,\n}) => {\n  const { state, clearCart } = useCart();\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(price);\n  };\n\n  const handleClearCart = async () => {\n    if (window.confirm('Are you sure you want to clear your cart?')) {\n      await clearCart();\n    }\n  };\n\n  if (!state.cart || state.cart.cartItems.length === 0) {\n    return null;\n  }\n\n  const subtotal = state.cart.totalAmount;\n  const tax = subtotal * 0.08; // 8% tax rate\n  const shipping = subtotal > 100 ? 0 : 15; // Free shipping over $100\n  const total = subtotal + tax + shipping;\n\n  return (\n    <div className={styles.cartSummary}>\n      <h2 className={styles.title}>Order Summary</h2>\n      \n      <div className={styles.summaryDetails}>\n        {/* Items Count */}\n        <div className={styles.summaryRow}>\n          <span className={styles.label}>Items ({state.cart.totalItems})</span>\n          <span className={styles.value}>{formatPrice(subtotal)}</span>\n        </div>\n\n        {/* Shipping */}\n        <div className={styles.summaryRow}>\n          <span className={styles.label}>\n            Shipping\n            {shipping === 0 && <span className={styles.freeShipping}> (Free)</span>}\n          </span>\n          <span className={styles.value}>\n            {shipping === 0 ? 'Free' : formatPrice(shipping)}\n          </span>\n        </div>\n\n        {/* Tax */}\n        <div className={styles.summaryRow}>\n          <span className={styles.label}>Tax</span>\n          <span className={styles.value}>{formatPrice(tax)}</span>\n        </div>\n\n        {/* Divider */}\n        <div className={styles.divider}></div>\n\n        {/* Total */}\n        <div className={styles.totalRow}>\n          <span className={styles.totalLabel}>Total</span>\n          <span className={styles.totalValue}>{formatPrice(total)}</span>\n        </div>\n      </div>\n\n      {/* Free Shipping Notice */}\n      {shipping > 0 && (\n        <div className={styles.shippingNotice}>\n          <svg className={styles.infoIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n            <path d=\"M12 16v-4\"/>\n            <path d=\"M12 8h.01\"/>\n          </svg>\n          <span>\n            Add {formatPrice(100 - subtotal)} more for free shipping\n          </span>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className={styles.actionButtons}>\n        {showCheckoutButton && (\n          <Link href=\"/checkout\" className={styles.checkoutBtn}>\n            <svg className={styles.checkoutIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M9 12l2 2 4-4\"/>\n            </svg>\n            Proceed to Checkout\n          </Link>\n        )}\n\n        {showClearButton && (\n          <button\n            onClick={handleClearCart}\n            disabled={state.isLoading}\n            className={styles.clearBtn}\n          >\n            <svg className={styles.clearIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M3 6h18\"/>\n              <path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/>\n              <path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"/>\n            </svg>\n            Clear Cart\n          </button>\n        )}\n      </div>\n\n      {/* Security Notice */}\n      <div className={styles.securityNotice}>\n        <svg className={styles.securityIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n          <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"/>\n          <path d=\"M9 12l2 2 4-4\"/>\n        </svg>\n        <span>Secure Checkout</span>\n      </div>\n    </div>\n  );\n};\n\nexport default CartSummary;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAYA,MAAM,cAA0C,CAAC,EAC/C,qBAAqB,IAAI,EACzB,kBAAkB,IAAI,EACvB;IACC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEnC,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,kBAAkB;QACtB,IAAI,OAAO,OAAO,CAAC,8CAA8C;YAC/D,MAAM;QACR;IACF;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;QACpD,OAAO;IACT;IAEA,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW;IACvC,MAAM,MAAM,WAAW,MAAM,cAAc;IAC3C,MAAM,WAAW,WAAW,MAAM,IAAI,IAAI,0BAA0B;IACpE,MAAM,QAAQ,WAAW,MAAM;IAE/B,qBACE,8OAAC;QAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,WAAW;;0BAChC,8OAAC;gBAAG,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;0BAAE;;;;;;0BAE7B,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,cAAc;;kCAEnC,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;gCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;;oCAAE;oCAAQ,MAAM,IAAI,CAAC,UAAU;oCAAC;;;;;;;0CAC7D,8OAAC;gCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;0CAAG,YAAY;;;;;;;;;;;;kCAI9C,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;gCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;;oCAAE;oCAE5B,aAAa,mBAAK,8OAAC;wCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;kDAAE;;;;;;;;;;;;0CAE3D,8OAAC;gCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;0CAC1B,aAAa,IAAI,SAAS,YAAY;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;gCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;0CAC/B,8OAAC;gCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;0CAAG,YAAY;;;;;;;;;;;;kCAI9C,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,OAAO;;;;;;kCAG9B,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,QAAQ;;0CAC7B,8OAAC;gCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;0CAAE;;;;;;0CACpC,8OAAC;gCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;0CAAG,YAAY;;;;;;;;;;;;;;;;;;YAKpD,WAAW,mBACV,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,QAAQ;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CACtE,8OAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,8OAAC;gCAAK,GAAE;;;;;;0CACR,8OAAC;gCAAK,GAAE;;;;;;;;;;;;kCAEV,8OAAC;;4BAAK;4BACC,YAAY,MAAM;4BAAU;;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,aAAa;;oBACjC,oCACC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAW,mKAAA,CAAA,UAAM,CAAC,WAAW;;0CAClD,8OAAC;gCAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;0CAC1E,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;oBAKT,iCACC,8OAAC;wBACC,SAAS;wBACT,UAAU,MAAM,SAAS;wBACzB,WAAW,mKAAA,CAAA,UAAM,CAAC,QAAQ;;0CAE1B,8OAAC;gCAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,SAAS;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;;kDACvE,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;;;;;;;4BACJ;;;;;;;;;;;;;0BAOZ,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1E,8OAAC;gCAAK,GAAE;;;;;;0CACR,8OAAC;gCAAK,GAAE;;;;;;;;;;;;kCAEV,8OAAC;kCAAK;;;;;;;;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/cart/cart.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"additionalActions\": \"cart-module__-RJi4G__additionalActions\",\n  \"cartContent\": \"cart-module__-RJi4G__cartContent\",\n  \"cartItems\": \"cart-module__-RJi4G__cartItems\",\n  \"contactLink\": \"cart-module__-RJi4G__contactLink\",\n  \"container\": \"cart-module__-RJi4G__container\",\n  \"continueShoppingLink\": \"cart-module__-RJi4G__continueShoppingLink\",\n  \"emptyCart\": \"cart-module__-RJi4G__emptyCart\",\n  \"emptyIcon\": \"cart-module__-RJi4G__emptyIcon\",\n  \"emptyMessage\": \"cart-module__-RJi4G__emptyMessage\",\n  \"emptyTitle\": \"cart-module__-RJi4G__emptyTitle\",\n  \"errorIcon\": \"cart-module__-RJi4G__errorIcon\",\n  \"errorMessage\": \"cart-module__-RJi4G__errorMessage\",\n  \"header\": \"cart-module__-RJi4G__header\",\n  \"helpSection\": \"cart-module__-RJi4G__helpSection\",\n  \"itemsHeader\": \"cart-module__-RJi4G__itemsHeader\",\n  \"itemsList\": \"cart-module__-RJi4G__itemsList\",\n  \"loadingContainer\": \"cart-module__-RJi4G__loadingContainer\",\n  \"loadingSpinner\": \"cart-module__-RJi4G__loadingSpinner\",\n  \"shippingInfo\": \"cart-module__-RJi4G__shippingInfo\",\n  \"shopIcon\": \"cart-module__-RJi4G__shopIcon\",\n  \"shopNowBtn\": \"cart-module__-RJi4G__shopNowBtn\",\n  \"spin\": \"cart-module__-RJi4G__spin\",\n  \"subtitle\": \"cart-module__-RJi4G__subtitle\",\n  \"title\": \"cart-module__-RJi4G__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/cart/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useCart } from '@/contexts/CartContext';\nimport CartItem from '@/components/cart/CartItem/CartItem';\nimport CartSummary from '@/components/cart/CartSummary/CartSummary';\nimport styles from './cart.module.css';\n\nexport default function CartPage() {\n  const { state } = useCart();\n\n  if (state.isLoading) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.loadingContainer}>\n          <div className={styles.loadingSpinner}></div>\n          <p>Loading your cart...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!state.cart || state.cart.cartItems.length === 0) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.emptyCart}>\n          <div className={styles.emptyIcon}>\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\n            </svg>\n          </div>\n          <h1 className={styles.emptyTitle}>Your Cart is Empty</h1>\n          <p className={styles.emptyMessage}>\n            Looks like you haven't added any items to your cart yet. \n            Start shopping to fill it up!\n          </p>\n          <Link href=\"/products\" className={styles.shopNowBtn}>\n            <svg className={styles.shopIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\n            </svg>\n            Start Shopping\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.container}>\n      {/* Header */}\n      <div className={styles.header}>\n        <h1 className={styles.title}>Shopping Cart</h1>\n        <p className={styles.subtitle}>\n          {state.cart.totalItems} {state.cart.totalItems === 1 ? 'item' : 'items'} in your cart\n        </p>\n      </div>\n\n      {/* Error Message */}\n      {state.error && (\n        <div className={styles.errorMessage}>\n          <svg className={styles.errorIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n            <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"/>\n            <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"/>\n          </svg>\n          <span>{state.error}</span>\n        </div>\n      )}\n\n      {/* Cart Content */}\n      <div className={styles.cartContent}>\n        {/* Cart Items */}\n        <div className={styles.cartItems}>\n          <div className={styles.itemsHeader}>\n            <h2>Items</h2>\n            <Link href=\"/products\" className={styles.continueShoppingLink}>\n              Continue Shopping\n            </Link>\n          </div>\n          \n          <div className={styles.itemsList}>\n            {state.cart.cartItems.map((item) => (\n              <CartItem key={item.id} item={item} />\n            ))}\n          </div>\n        </div>\n\n        {/* Cart Summary */}\n        <div className={styles.cartSummaryContainer}>\n          <CartSummary />\n        </div>\n      </div>\n\n      {/* Additional Actions */}\n      <div className={styles.additionalActions}>\n        <div className={styles.helpSection}>\n          <h3>Need Help?</h3>\n          <p>\n            Have questions about your order? \n            <Link href=\"/contact\" className={styles.contactLink}> Contact us</Link> \n            or call (555) 123-4567\n          </p>\n        </div>\n\n        <div className={styles.shippingInfo}>\n          <h3>Shipping Information</h3>\n          <ul>\n            <li>Free shipping on orders over $100</li>\n            <li>Standard delivery: 5-7 business days</li>\n            <li>Express delivery: 2-3 business days</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExB,IAAI,MAAM,SAAS,EAAE;QACnB,qBACE,8OAAC;YAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,8OAAC;gBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,gBAAgB;;kCACrC,8OAAC;wBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,cAAc;;;;;;kCACrC,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;QACpD,qBACE,8OAAC;YAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,8OAAC;gBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,8OAAC;wBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;kCAC9B,cAAA,8OAAC;4BAAI,SAAQ;4BAAY,MAAK;4BAAO,QAAO;sCAC1C,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAGZ,8OAAC;wBAAG,WAAW,sIAAA,CAAA,UAAM,CAAC,UAAU;kCAAE;;;;;;kCAClC,8OAAC;wBAAE,WAAW,sIAAA,CAAA,UAAM,CAAC,YAAY;kCAAE;;;;;;kCAInC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAW,sIAAA,CAAA,UAAM,CAAC,UAAU;;0CACjD,8OAAC;gCAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,QAAQ;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;0CACtE,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;;;;;;;;;;;;IAMhB;IAEA,qBACE,8OAAC;QAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,8OAAC;gBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,8OAAC;wBAAG,WAAW,sIAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC7B,8OAAC;wBAAE,WAAW,sIAAA,CAAA,UAAM,CAAC,QAAQ;;4BAC1B,MAAM,IAAI,CAAC,UAAU;4BAAC;4BAAE,MAAM,IAAI,CAAC,UAAU,KAAK,IAAI,SAAS;4BAAQ;;;;;;;;;;;;;YAK3E,MAAM,KAAK,kBACV,8OAAC;gBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,8OAAC;wBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CACvE,8OAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,8OAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;;;;;;0CAC/B,8OAAC;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAK,IAAG;;;;;;;;;;;;kCAEjC,8OAAC;kCAAM,MAAM,KAAK;;;;;;;;;;;;0BAKtB,8OAAC;gBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,WAAW;;kCAEhC,8OAAC;wBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;;0CAC9B,8OAAC;gCAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAW,sIAAA,CAAA,UAAM,CAAC,oBAAoB;kDAAE;;;;;;;;;;;;0CAKjE,8OAAC;gCAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,SAAS;0CAC7B,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC,kJAAA,CAAA,UAAQ;wCAAe,MAAM;uCAAf,KAAK,EAAE;;;;;;;;;;;;;;;;kCAM5B,8OAAC;wBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,oBAAoB;kCACzC,cAAA,8OAAC,wJAAA,CAAA,UAAW;;;;;;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,iBAAiB;;kCACtC,8OAAC;wBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;;oCAAE;kDAED,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAW,sIAAA,CAAA,UAAM,CAAC,WAAW;kDAAE;;;;;;oCAAkB;;;;;;;;;;;;;kCAK3E,8OAAC;wBAAI,WAAW,sIAAA,CAAA,UAAM,CAAC,YAAY;;0CACjC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;;kDACC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}]}