{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductCard/productCard.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionButtons\": \"productCard-module__UIKE7W__actionButtons\",\n  \"addToCartBtn\": \"productCard-module__UIKE7W__addToCartBtn\",\n  \"addToCartSection\": \"productCard-module__UIKE7W__addToCartSection\",\n  \"cartIcon\": \"productCard-module__UIKE7W__cartIcon\",\n  \"collection\": \"productCard-module__UIKE7W__collection\",\n  \"imageContainer\": \"productCard-module__UIKE7W__imageContainer\",\n  \"inStock\": \"productCard-module__UIKE7W__inStock\",\n  \"loading\": \"productCard-module__UIKE7W__loading\",\n  \"outOfStock\": \"productCard-module__UIKE7W__outOfStock\",\n  \"outOfStockOverlay\": \"productCard-module__UIKE7W__outOfStockOverlay\",\n  \"price\": \"productCard-module__UIKE7W__price\",\n  \"priceContainer\": \"productCard-module__UIKE7W__priceContainer\",\n  \"productCard\": \"productCard-module__UIKE7W__productCard\",\n  \"productDescription\": \"productCard-module__UIKE7W__productDescription\",\n  \"productImage\": \"productCard-module__UIKE7W__productImage\",\n  \"productInfo\": \"productCard-module__UIKE7W__productInfo\",\n  \"productName\": \"productCard-module__UIKE7W__productName\",\n  \"quantity\": \"productCard-module__UIKE7W__quantity\",\n  \"quantityBtn\": \"productCard-module__UIKE7W__quantityBtn\",\n  \"quantitySelector\": \"productCard-module__UIKE7W__quantitySelector\",\n  \"spin\": \"productCard-module__UIKE7W__spin\",\n  \"stockInfo\": \"productCard-module__UIKE7W__stockInfo\",\n  \"viewDetailsBtn\": \"productCard-module__UIKE7W__viewDetailsBtn\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductCard/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Product } from '@/services/types/entities';\nimport { useCart } from '@/contexts/CartContext';\nimport styles from './productCard.module.css';\n\ninterface ProductCardProps {\n  product: Product;\n  showAddToCart?: boolean;\n  showViewDetails?: boolean;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({\n  product,\n  showAddToCart = true,\n  showViewDetails = true,\n}) => {\n  const { addToCart, state } = useCart();\n  const [isAddingToCart, setIsAddingToCart] = useState(false);\n  const [quantity, setQuantity] = useState(1);\n\n  const handleAddToCart = async () => {\n    try {\n      setIsAddingToCart(true);\n      await addToCart(product.id, quantity);\n      // You could add a toast notification here\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      // You could add error notification here\n    } finally {\n      setIsAddingToCart(false);\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(price);\n  };\n\n  const mainImage = product.images && product.images.length > 0 \n    ? product.images[0] \n    : '/images/placeholder-product.jpg';\n\n  const isInStock = product.stock > 0;\n\n  return (\n    <div className={styles.productCard}>\n      {/* Product Image */}\n      <div className={styles.imageContainer}>\n        <img\n          src={mainImage}\n          alt={product.name}\n          className={styles.productImage}\n        />\n        {!isInStock && (\n          <div className={styles.outOfStockOverlay}>\n            <span>Out of Stock</span>\n          </div>\n        )}\n      </div>\n\n      {/* Product Info */}\n      <div className={styles.productInfo}>\n        <h3 className={styles.productName}>{product.name}</h3>\n        \n        {product.description && (\n          <p className={styles.productDescription}>\n            {product.description.length > 100 \n              ? `${product.description.substring(0, 100)}...` \n              : product.description}\n          </p>\n        )}\n\n        <div className={styles.priceContainer}>\n          <span className={styles.price}>{formatPrice(product.price)}</span>\n          {product.collection && (\n            <span className={styles.collection}>{product.collection.name}</span>\n          )}\n        </div>\n\n        {/* Stock Info */}\n        <div className={styles.stockInfo}>\n          {isInStock ? (\n            <span className={styles.inStock}>\n              {product.stock > 10 ? 'In Stock' : `Only ${product.stock} left`}\n            </span>\n          ) : (\n            <span className={styles.outOfStock}>Out of Stock</span>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div className={styles.actionButtons}>\n          {showViewDetails && (\n            <Link href={`/products/${product.id}`} className={styles.viewDetailsBtn}>\n              View Details\n            </Link>\n          )}\n          \n          {showAddToCart && isInStock && (\n            <div className={styles.addToCartSection}>\n              <div className={styles.quantitySelector}>\n                <button\n                  type=\"button\"\n                  onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                  className={styles.quantityBtn}\n                  disabled={quantity <= 1}\n                >\n                  -\n                </button>\n                <span className={styles.quantity}>{quantity}</span>\n                <button\n                  type=\"button\"\n                  onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}\n                  className={styles.quantityBtn}\n                  disabled={quantity >= product.stock}\n                >\n                  +\n                </button>\n              </div>\n              \n              <button\n                onClick={handleAddToCart}\n                disabled={isAddingToCart || state.isLoading}\n                className={styles.addToCartBtn}\n              >\n                {isAddingToCart ? (\n                  <span className={styles.loading}>Adding...</span>\n                ) : (\n                  <>\n                    <svg className={styles.cartIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                      <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\n                    </svg>\n                    Add to Cart\n                  </>\n                )}\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;;;AANA;;;;;AAcA,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACvB;;IACC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,kBAAkB;QACtB,IAAI;YACF,kBAAkB;YAClB,MAAM,UAAU,QAAQ,EAAE,EAAE;QAC5B,0CAA0C;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,wCAAwC;QAC1C,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,YAAY,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IACxD,QAAQ,MAAM,CAAC,EAAE,GACjB;IAEJ,MAAM,YAAY,QAAQ,KAAK,GAAG;IAElC,qBACE,6LAAC;QAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,WAAW;;0BAEhC,6LAAC;gBAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,6LAAC;wBACC,KAAK;wBACL,KAAK,QAAQ,IAAI;wBACjB,WAAW,0KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;oBAE/B,CAAC,2BACA,6LAAC;wBAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,iBAAiB;kCACtC,cAAA,6LAAC;sCAAK;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,WAAW;;kCAChC,6LAAC;wBAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,WAAW;kCAAG,QAAQ,IAAI;;;;;;oBAE/C,QAAQ,WAAW,kBAClB,6LAAC;wBAAE,WAAW,0KAAA,CAAA,UAAM,CAAC,kBAAkB;kCACpC,QAAQ,WAAW,CAAC,MAAM,GAAG,MAC1B,GAAG,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAC7C,QAAQ,WAAW;;;;;;kCAI3B,6LAAC;wBAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;;0CACnC,6LAAC;gCAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,KAAK;0CAAG,YAAY,QAAQ,KAAK;;;;;;4BACxD,QAAQ,UAAU,kBACjB,6LAAC;gCAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,UAAU;0CAAG,QAAQ,UAAU,CAAC,IAAI;;;;;;;;;;;;kCAKhE,6LAAC;wBAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,SAAS;kCAC7B,0BACC,6LAAC;4BAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,OAAO;sCAC5B,QAAQ,KAAK,GAAG,KAAK,aAAa,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC;;;;;iDAGjE,6LAAC;4BAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,UAAU;sCAAE;;;;;;;;;;;kCAKxC,6LAAC;wBAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;;4BACjC,iCACC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAAE,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;0CAAE;;;;;;4BAK1E,iBAAiB,2BAChB,6LAAC;gCAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,gBAAgB;;kDACrC,6LAAC;wCAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,gBAAgB;;0DACrC,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;gDAClD,WAAW,0KAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,UAAU,YAAY;0DACvB;;;;;;0DAGD,6LAAC;gDAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,QAAQ;0DAAG;;;;;;0DACnC,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,WAAW;gDAC9D,WAAW,0KAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,UAAU,YAAY,QAAQ,KAAK;0DACpC;;;;;;;;;;;;kDAKH,6LAAC;wCACC,SAAS;wCACT,UAAU,kBAAkB,MAAM,SAAS;wCAC3C,WAAW,0KAAA,CAAA,UAAM,CAAC,YAAY;kDAE7B,+BACC,6LAAC;4CAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,OAAO;sDAAE;;;;;iEAEjC;;8DACE,6LAAC;oDAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,QAAQ;oDAAE,SAAQ;oDAAY,MAAK;oDAAO,QAAO;8DACtE,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;gDACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;GArIM;;QAKyB,kIAAA,CAAA,UAAO;;;KALhC;uCAuIS", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductGrid/productGrid.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"emptyContainer\": \"productGrid-module__d3162q__emptyContainer\",\n  \"emptyIcon\": \"productGrid-module__d3162q__emptyIcon\",\n  \"emptyMessage\": \"productGrid-module__d3162q__emptyMessage\",\n  \"emptyTitle\": \"productGrid-module__d3162q__emptyTitle\",\n  \"loadingButton\": \"productGrid-module__d3162q__loadingButton\",\n  \"loadingCard\": \"productGrid-module__d3162q__loadingCard\",\n  \"loadingContainer\": \"productGrid-module__d3162q__loadingContainer\",\n  \"loadingContent\": \"productGrid-module__d3162q__loadingContent\",\n  \"loadingDescription\": \"productGrid-module__d3162q__loadingDescription\",\n  \"loadingGrid\": \"productGrid-module__d3162q__loadingGrid\",\n  \"loadingImage\": \"productGrid-module__d3162q__loadingImage\",\n  \"loadingPrice\": \"productGrid-module__d3162q__loadingPrice\",\n  \"loadingTitle\": \"productGrid-module__d3162q__loadingTitle\",\n  \"productGrid\": \"productGrid-module__d3162q__productGrid\",\n  \"pulse\": \"productGrid-module__d3162q__pulse\",\n  \"shimmer\": \"productGrid-module__d3162q__shimmer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductGrid/ProductGrid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Product } from '@/services/types/entities';\nimport ProductCard from '../ProductCard/ProductCard';\nimport styles from './productGrid.module.css';\n\ninterface ProductGridProps {\n  products: Product[];\n  isLoading?: boolean;\n  showAddToCart?: boolean;\n  showViewDetails?: boolean;\n  emptyMessage?: string;\n}\n\nconst ProductGrid: React.FC<ProductGridProps> = ({\n  products,\n  isLoading = false,\n  showAddToCart = true,\n  showViewDetails = true,\n  emptyMessage = 'No products found.',\n}) => {\n  if (isLoading) {\n    return (\n      <div className={styles.loadingContainer}>\n        <div className={styles.loadingGrid}>\n          {Array.from({ length: 6 }).map((_, index) => (\n            <div key={index} className={styles.loadingCard}>\n              <div className={styles.loadingImage}></div>\n              <div className={styles.loadingContent}>\n                <div className={styles.loadingTitle}></div>\n                <div className={styles.loadingDescription}></div>\n                <div className={styles.loadingPrice}></div>\n                <div className={styles.loadingButton}></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (products.length === 0) {\n    return (\n      <div className={styles.emptyContainer}>\n        <div className={styles.emptyIcon}>\n          <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n            <path d=\"M16 16s-1.5-2-4-2-4 2-4 2\"/>\n            <line x1=\"9\" y1=\"9\" x2=\"9.01\" y2=\"9\"/>\n            <line x1=\"15\" y1=\"9\" x2=\"15.01\" y2=\"9\"/>\n          </svg>\n        </div>\n        <h3 className={styles.emptyTitle}>No Products Found</h3>\n        <p className={styles.emptyMessage}>{emptyMessage}</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.productGrid}>\n      {products.map((product) => (\n        <ProductCard\n          key={product.id}\n          product={product}\n          showAddToCart={showAddToCart}\n          showViewDetails={showViewDetails}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default ProductGrid;\n"], "names": [], "mappings": ";;;;AAIA;AACA;AALA;;;;AAeA,MAAM,cAA0C,CAAC,EAC/C,QAAQ,EACR,YAAY,KAAK,EACjB,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACtB,eAAe,oBAAoB,EACpC;IACC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,gBAAgB;sBACrC,cAAA,6LAAC;gBAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,WAAW;0BAC/B,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;wBAAgB,WAAW,0KAAA,CAAA,UAAM,CAAC,WAAW;;0CAC5C,6LAAC;gCAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;0CACnC,6LAAC;gCAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;;kDACnC,6LAAC;wCAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDACnC,6LAAC;wCAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,kBAAkB;;;;;;kDACzC,6LAAC;wCAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDACnC,6LAAC;wCAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;;;;;;;;;;;;;uBAN9B;;;;;;;;;;;;;;;IAapB;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,6LAAC;YAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;;8BACnC,6LAAC;oBAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,SAAS;8BAC9B,cAAA,6LAAC;wBAAI,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1C,6LAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAO,IAAG;;;;;;0CACjC,6LAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAI,IAAG;gCAAQ,IAAG;;;;;;;;;;;;;;;;;8BAGvC,6LAAC;oBAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,UAAU;8BAAE;;;;;;8BAClC,6LAAC;oBAAE,WAAW,0KAAA,CAAA,UAAM,CAAC,YAAY;8BAAG;;;;;;;;;;;;IAG1C;IAEA,qBACE,6LAAC;QAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,WAAW;kBAC/B,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,+JAAA,CAAA,UAAW;gBAEV,SAAS;gBACT,eAAe;gBACf,iBAAiB;eAHZ,QAAQ,EAAE;;;;;;;;;;AAQzB;KAxDM;uCA0DS", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/index.ts"], "sourcesContent": ["// Product Components\nexport { default as ProductCard } from './ProductCard/ProductCard';\nexport { default as ProductGrid } from './ProductGrid/ProductGrid';\n"], "names": [], "mappings": "AAAA,qBAAqB;;AACrB;AACA", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/products/products.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkboxLabel\": \"products-module__E8alaG__checkboxLabel\",\n  \"clearFilters\": \"products-module__E8alaG__clearFilters\",\n  \"container\": \"products-module__E8alaG__container\",\n  \"content\": \"products-module__E8alaG__content\",\n  \"filterGroup\": \"products-module__E8alaG__filterGroup\",\n  \"filterIcon\": \"products-module__E8alaG__filterIcon\",\n  \"filterSelect\": \"products-module__E8alaG__filterSelect\",\n  \"filterToggle\": \"products-module__E8alaG__filterToggle\",\n  \"filtersHeader\": \"products-module__E8alaG__filtersHeader\",\n  \"filtersSidebar\": \"products-module__E8alaG__filtersSidebar\",\n  \"header\": \"products-module__E8alaG__header\",\n  \"headerActions\": \"products-module__E8alaG__headerActions\",\n  \"headerContent\": \"products-module__E8alaG__headerContent\",\n  \"priceInput\": \"products-module__E8alaG__priceInput\",\n  \"priceRange\": \"products-module__E8alaG__priceRange\",\n  \"productsSection\": \"products-module__E8alaG__productsSection\",\n  \"resultsCount\": \"products-module__E8alaG__resultsCount\",\n  \"searchInput\": \"products-module__E8alaG__searchInput\",\n  \"showFilters\": \"products-module__E8alaG__showFilters\",\n  \"subtitle\": \"products-module__E8alaG__subtitle\",\n  \"title\": \"products-module__E8alaG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Product, Collection } from '@/services/types/entities';\nimport { productService, collectionService } from '@/services';\nimport { ProductGrid } from '@/components/products';\nimport styles from './products.module.css';\n\ninterface FilterState {\n  search: string;\n  collectionId: number | '';\n  priceRange: {\n    min: number;\n    max: number;\n  };\n  inStockOnly: boolean;\n  sortBy: 'name' | 'price' | 'newest';\n  sortDirection: 'asc' | 'desc';\n}\n\nexport default function ProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [collections, setCollections] = useState<Collection[]>([]);\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showFilters, setShowFilters] = useState(false);\n  \n  const [filters, setFilters] = useState<FilterState>({\n    search: '',\n    collectionId: '',\n    priceRange: { min: 0, max: 10000 },\n    inStockOnly: false,\n    sortBy: 'name',\n    sortDirection: 'asc'\n  });\n\n  // Fetch data on component mount\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  // Apply filters when products or filters change\n  useEffect(() => {\n    applyFilters();\n  }, [products, filters]);\n\n  const fetchData = async () => {\n    try {\n      setIsLoading(true);\n      const [productsData, collectionsData] = await Promise.all([\n        productService.get.getAll(),\n        collectionService.get.getAll(),\n      ]);\n      setProducts(productsData);\n      setCollections(collectionsData);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const applyFilters = () => {\n    let filtered = [...products];\n\n    // Search filter\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filtered = filtered.filter(product =>\n        product.name.toLowerCase().includes(searchLower) ||\n        product.description?.toLowerCase().includes(searchLower) ||\n        product.tags.some(tag => tag.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Collection filter\n    if (filters.collectionId) {\n      filtered = filtered.filter(product => product.collectionId === filters.collectionId);\n    }\n\n    // Price range filter\n    filtered = filtered.filter(product =>\n      product.price >= filters.priceRange.min && product.price <= filters.priceRange.max\n    );\n\n    // Stock filter\n    if (filters.inStockOnly) {\n      filtered = filtered.filter(product => product.stock > 0);\n    }\n\n    // Sorting\n    filtered.sort((a, b) => {\n      let comparison = 0;\n      \n      switch (filters.sortBy) {\n        case 'name':\n          comparison = a.name.localeCompare(b.name);\n          break;\n        case 'price':\n          comparison = a.price - b.price;\n          break;\n        case 'newest':\n          comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n          break;\n      }\n      \n      return filters.sortDirection === 'desc' ? -comparison : comparison;\n    });\n\n    setFilteredProducts(filtered);\n  };\n\n  const handleFilterChange = (key: keyof FilterState, value: any) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handlePriceRangeChange = (min: number, max: number) => {\n    setFilters(prev => ({\n      ...prev,\n      priceRange: { min, max }\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      search: '',\n      collectionId: '',\n      priceRange: { min: 0, max: 10000 },\n      inStockOnly: false,\n      sortBy: 'name',\n      sortDirection: 'asc'\n    });\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(price);\n  };\n\n  return (\n    <div className={styles.container}>\n      {/* Header */}\n      <div className={styles.header}>\n        <div className={styles.headerContent}>\n          <h1 className={styles.title}>Our Products</h1>\n          <p className={styles.subtitle}>\n            Discover our exquisite collection of handcrafted cast stone pieces\n          </p>\n        </div>\n        \n        <div className={styles.headerActions}>\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className={styles.filterToggle}\n          >\n            <svg className={styles.filterIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <polygon points=\"22,3 2,3 10,12.46 10,19 14,21 14,12.46\"/>\n            </svg>\n            Filters\n          </button>\n          \n          <div className={styles.resultsCount}>\n            {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'}\n          </div>\n        </div>\n      </div>\n\n      <div className={styles.content}>\n        {/* Filters Sidebar */}\n        <div className={`${styles.filtersSidebar} ${showFilters ? styles.showFilters : ''}`}>\n          <div className={styles.filtersHeader}>\n            <h3>Filters</h3>\n            <button onClick={clearFilters} className={styles.clearFilters}>\n              Clear All\n            </button>\n          </div>\n\n          {/* Search */}\n          <div className={styles.filterGroup}>\n            <label>Search</label>\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={filters.search}\n              onChange={(e) => handleFilterChange('search', e.target.value)}\n              className={styles.searchInput}\n            />\n          </div>\n\n          {/* Collection Filter */}\n          <div className={styles.filterGroup}>\n            <label>Collection</label>\n            <select\n              value={filters.collectionId}\n              onChange={(e) => handleFilterChange('collectionId', e.target.value ? parseInt(e.target.value) : '')}\n              className={styles.filterSelect}\n            >\n              <option value=\"\">All Collections</option>\n              {collections.map(collection => (\n                <option key={collection.id} value={collection.id}>\n                  {collection.name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Price Range */}\n          <div className={styles.filterGroup}>\n            <label>Price Range</label>\n            <div className={styles.priceRange}>\n              <input\n                type=\"number\"\n                placeholder=\"Min\"\n                value={filters.priceRange.min}\n                onChange={(e) => handlePriceRangeChange(parseInt(e.target.value) || 0, filters.priceRange.max)}\n                className={styles.priceInput}\n              />\n              <span>to</span>\n              <input\n                type=\"number\"\n                placeholder=\"Max\"\n                value={filters.priceRange.max}\n                onChange={(e) => handlePriceRangeChange(filters.priceRange.min, parseInt(e.target.value) || 10000)}\n                className={styles.priceInput}\n              />\n            </div>\n          </div>\n\n          {/* Stock Filter */}\n          <div className={styles.filterGroup}>\n            <label className={styles.checkboxLabel}>\n              <input\n                type=\"checkbox\"\n                checked={filters.inStockOnly}\n                onChange={(e) => handleFilterChange('inStockOnly', e.target.checked)}\n              />\n              In Stock Only\n            </label>\n          </div>\n\n          {/* Sort Options */}\n          <div className={styles.filterGroup}>\n            <label>Sort By</label>\n            <select\n              value={`${filters.sortBy}-${filters.sortDirection}`}\n              onChange={(e) => {\n                const [sortBy, sortDirection] = e.target.value.split('-');\n                handleFilterChange('sortBy', sortBy);\n                handleFilterChange('sortDirection', sortDirection);\n              }}\n              className={styles.filterSelect}\n            >\n              <option value=\"name-asc\">Name (A-Z)</option>\n              <option value=\"name-desc\">Name (Z-A)</option>\n              <option value=\"price-asc\">Price (Low to High)</option>\n              <option value=\"price-desc\">Price (High to Low)</option>\n              <option value=\"newest-desc\">Newest First</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Products Grid */}\n        <div className={styles.productsSection}>\n          <ProductGrid\n            products={filteredProducts}\n            isLoading={isLoading}\n            emptyMessage=\"No products match your current filters. Try adjusting your search criteria.\"\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AACA;AAAA;AACA;;;AANA;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,QAAQ;QACR,cAAc;QACd,YAAY;YAAE,KAAK;YAAG,KAAK;QAAM;QACjC,aAAa;QACb,QAAQ;QACR,eAAe;IACjB;IAEA,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACxD,8JAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,MAAM;gBACzB,iKAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,MAAM;aAC7B;YACD,YAAY;YACZ,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW;eAAI;SAAS;QAE5B,gBAAgB;QAChB,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;YAC9C,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACpC,QAAQ,WAAW,EAAE,cAAc,SAAS,gBAC5C,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;QAExD;QAEA,oBAAoB;QACpB,IAAI,QAAQ,YAAY,EAAE;YACxB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK,QAAQ,YAAY;QACrF;QAEA,qBAAqB;QACrB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,IAAI,QAAQ,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG;QAGpF,eAAe;QACf,IAAI,QAAQ,WAAW,EAAE;YACvB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,GAAG;QACxD;QAEA,UAAU;QACV,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,IAAI,aAAa;YAEjB,OAAQ,QAAQ,MAAM;gBACpB,KAAK;oBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;oBACxC;gBACF,KAAK;oBACH,aAAa,EAAE,KAAK,GAAG,EAAE,KAAK;oBAC9B;gBACF,KAAK;oBACH,aAAa,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;oBAC5E;YACJ;YAEA,OAAO,QAAQ,aAAa,KAAK,SAAS,CAAC,aAAa;QAC1D;QAEA,oBAAoB;IACtB;IAEA,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,yBAAyB,CAAC,KAAa;QAC3C,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,YAAY;oBAAE;oBAAK;gBAAI;YACzB,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,QAAQ;YACR,cAAc;YACd,YAAY;gBAAE,KAAK;gBAAG,KAAK;YAAM;YACjC,aAAa;YACb,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,6LAAC;gBAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,6LAAC;wBAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,6LAAC;gCAAG,WAAW,iJAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;0CAC7B,6LAAC;gCAAE,WAAW,iJAAA,CAAA,UAAM,CAAC,QAAQ;0CAAE;;;;;;;;;;;;kCAKjC,6LAAC;wBAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAW,iJAAA,CAAA,UAAM,CAAC,YAAY;;kDAE9B,6LAAC;wCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,UAAU;wCAAE,SAAQ;wCAAY,MAAK;wCAAO,QAAO;kDACxE,cAAA,6LAAC;4CAAQ,QAAO;;;;;;;;;;;oCACZ;;;;;;;0CAIR,6LAAC;gCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,YAAY;;oCAChC,iBAAiB,MAAM;oCAAC;oCAAE,iBAAiB,MAAM,KAAK,IAAI,YAAY;;;;;;;;;;;;;;;;;;;0BAK7E,6LAAC;gBAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,OAAO;;kCAE5B,6LAAC;wBAAI,WAAW,GAAG,iJAAA,CAAA,UAAM,CAAC,cAAc,CAAC,CAAC,EAAE,cAAc,iJAAA,CAAA,UAAM,CAAC,WAAW,GAAG,IAAI;;0CACjF,6LAAC;gCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;wCAAO,SAAS;wCAAc,WAAW,iJAAA,CAAA,UAAM,CAAC,YAAY;kDAAE;;;;;;;;;;;;0CAMjE,6LAAC;gCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAM;;;;;;kDACP,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO,QAAQ,MAAM;wCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAW,iJAAA,CAAA,UAAM,CAAC,WAAW;;;;;;;;;;;;0CAKjC,6LAAC;gCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAM;;;;;;kDACP,6LAAC;wCACC,OAAO,QAAQ,YAAY;wCAC3B,UAAU,CAAC,IAAM,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;wCAChG,WAAW,iJAAA,CAAA,UAAM,CAAC,YAAY;;0DAE9B,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,YAAY,GAAG,CAAC,CAAA,2BACf,6LAAC;oDAA2B,OAAO,WAAW,EAAE;8DAC7C,WAAW,IAAI;mDADL,WAAW,EAAE;;;;;;;;;;;;;;;;;0CAQhC,6LAAC;gCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAM;;;;;;kDACP,6LAAC;wCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,UAAU;;0DAC/B,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;gDAC7B,UAAU,CAAC,IAAM,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK,GAAG,QAAQ,UAAU,CAAC,GAAG;gDAC7F,WAAW,iJAAA,CAAA,UAAM,CAAC,UAAU;;;;;;0DAE9B,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;gDAC7B,UAAU,CAAC,IAAM,uBAAuB,QAAQ,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC5F,WAAW,iJAAA,CAAA,UAAM,CAAC,UAAU;;;;;;;;;;;;;;;;;;0CAMlC,6LAAC;gCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,WAAW;0CAChC,cAAA,6LAAC;oCAAM,WAAW,iJAAA,CAAA,UAAM,CAAC,aAAa;;sDACpC,6LAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,WAAW;4CAC5B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,OAAO;;;;;;wCACnE;;;;;;;;;;;;0CAMN,6LAAC;gCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAM;;;;;;kDACP,6LAAC;wCACC,OAAO,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,EAAE;wCACnD,UAAU,CAAC;4CACT,MAAM,CAAC,QAAQ,cAAc,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;4CACrD,mBAAmB,UAAU;4CAC7B,mBAAmB,iBAAiB;wCACtC;wCACA,WAAW,iJAAA,CAAA,UAAM,CAAC,YAAY;;0DAE9B,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC;wBAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,eAAe;kCACpC,cAAA,6LAAC,yMAAA,CAAA,cAAW;4BACV,UAAU;4BACV,WAAW;4BACX,cAAa;;;;;;;;;;;;;;;;;;;;;;;AAMzB;GAjQwB;KAAA", "debugId": null}}]}