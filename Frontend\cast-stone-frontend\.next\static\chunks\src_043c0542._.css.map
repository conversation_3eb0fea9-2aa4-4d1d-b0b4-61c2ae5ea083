{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartItem/cartItem.module.css"], "sourcesContent": ["/* Cart Item Styles - Magazine/Editorial Theme */\n.cartItem {\n  display: grid;\n  grid-template-columns: 120px 1fr auto auto auto;\n  gap: 1.5rem;\n  padding: 1.5rem;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  align-items: start;\n  transition: box-shadow 0.3s ease;\n}\n\n.cartItem:hover {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n/* Product Image */\n.imageContainer {\n  width: 120px;\n  height: 120px;\n  border-radius: 8px;\n  overflow: hidden;\n  background: #f8f9fa;\n}\n\n.productImage {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* Product Details */\n.productDetails {\n  min-width: 0; /* Allow text truncation */\n}\n\n.productName {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  line-height: 1.3;\n}\n\n.productDescription {\n  color: #6b5b4d;\n  font-size: 0.9rem;\n  line-height: 1.5;\n  margin: 0 0 0.75rem 0;\n}\n\n.productMeta {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  margin-bottom: 0.5rem;\n}\n\n.unitPrice {\n  color: #4a3728;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.collection {\n  color: #8b7355;\n  font-size: 0.85rem;\n  font-style: italic;\n}\n\n.stockStatus {\n  margin-top: 0.5rem;\n}\n\n.inStock {\n  color: #059669;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n.outOfStock {\n  color: #dc2626;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n/* Quantity Section */\n.quantitySection {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.5rem;\n  min-width: 120px;\n}\n\n.quantityLabel {\n  color: #4a3728;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-align: center;\n}\n\n.quantityControls {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 0.25rem;\n}\n\n.quantityBtn {\n  width: 32px;\n  height: 32px;\n  border: 1px solid #d1d5db;\n  background: white;\n  color: #4a3728;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.quantityBtn:hover:not(:disabled) {\n  background: #8b7355;\n  color: white;\n  border-color: #8b7355;\n}\n\n.quantityBtn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.quantity {\n  min-width: 40px;\n  text-align: center;\n  font-weight: 600;\n  color: #4a3728;\n  font-size: 1rem;\n}\n\n.updating {\n  color: #8b7355;\n  font-size: 0.75rem;\n  font-style: italic;\n}\n\n/* Price Section */\n.priceSection {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 0.25rem;\n  min-width: 120px;\n}\n\n.itemTotal {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.priceBreakdown {\n  color: #6b5b4d;\n  font-size: 0.85rem;\n}\n\n/* Remove Section */\n.removeSection {\n  display: flex;\n  align-items: flex-start;\n}\n\n.removeBtn {\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: transparent;\n  color: #dc2626;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.removeBtn:hover:not(:disabled) {\n  background: #fee2e2;\n  color: #b91c1c;\n}\n\n.removeBtn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.removeBtn svg {\n  width: 20px;\n  height: 20px;\n  stroke-width: 2;\n}\n\n.removing {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .cartItem {\n    grid-template-columns: 80px 1fr;\n    grid-template-areas: \n      \"image details\"\n      \"image quantity\"\n      \"image price\"\n      \"remove remove\";\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .imageContainer {\n    grid-area: image;\n    width: 80px;\n    height: 80px;\n  }\n\n  .productDetails {\n    grid-area: details;\n  }\n\n  .quantitySection {\n    grid-area: quantity;\n    flex-direction: row;\n    align-items: center;\n    justify-content: flex-start;\n    min-width: auto;\n  }\n\n  .quantityLabel {\n    margin-right: 0.5rem;\n  }\n\n  .priceSection {\n    grid-area: price;\n    align-items: flex-start;\n    min-width: auto;\n  }\n\n  .removeSection {\n    grid-area: remove;\n    justify-content: center;\n    margin-top: 0.5rem;\n  }\n\n  .productName {\n    font-size: 1.1rem;\n  }\n\n  .itemTotal {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .cartItem {\n    grid-template-columns: 1fr;\n    grid-template-areas: \n      \"image\"\n      \"details\"\n      \"quantity\"\n      \"price\"\n      \"remove\";\n    text-align: center;\n  }\n\n  .imageContainer {\n    width: 120px;\n    height: 120px;\n    margin: 0 auto;\n  }\n\n  .quantitySection {\n    justify-content: center;\n  }\n\n  .priceSection {\n    align-items: center;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;;;;AAQA;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;;;;;;AAeA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAMA;EACE;;;;;;;;;;EAWA;;;;;;EAMA;;;;EAIA;;;;;;;;EAQA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;;AAKF;EACE;;;;;;;;;;EAWA;;;;;;EAMA;;;;EAIA", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartSummary/cartSummary.module.css"], "sourcesContent": ["/* Cart Summary Styles - Magazine/Editorial Theme */\n.cartSummary {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  position: sticky;\n  top: 2rem;\n  height: fit-content;\n}\n\n.title {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 1.5rem 0;\n  text-align: center;\n  border-bottom: 2px solid #f3f4f6;\n  padding-bottom: 1rem;\n}\n\n/* Summary Details */\n.summaryDetails {\n  margin-bottom: 1.5rem;\n}\n\n.summaryRow {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.label {\n  color: #6b5b4d;\n  font-size: 1rem;\n  font-weight: 500;\n}\n\n.value {\n  color: #4a3728;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.freeShipping {\n  color: #059669;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n.divider {\n  height: 1px;\n  background: #e5e7eb;\n  margin: 1rem 0;\n}\n\n.totalRow {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 0;\n  border-top: 2px solid #4a3728;\n  margin-top: 1rem;\n}\n\n.totalLabel {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n}\n\n.totalValue {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n/* Shipping Notice */\n.shippingNotice {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: #f0f9ff;\n  border: 1px solid #bae6fd;\n  border-radius: 6px;\n  padding: 0.75rem;\n  margin-bottom: 1.5rem;\n  color: #0369a1;\n  font-size: 0.9rem;\n}\n\n.infoIcon {\n  width: 16px;\n  height: 16px;\n  flex-shrink: 0;\n}\n\n/* Action Buttons */\n.actionButtons {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.checkoutBtn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 1rem 1.5rem;\n  background: #8b7355;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-weight: 600;\n  font-size: 1rem;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.checkoutBtn:hover {\n  background: #6d5a47;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);\n}\n\n.checkoutIcon {\n  width: 20px;\n  height: 20px;\n  stroke-width: 2;\n}\n\n.clearBtn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  background: transparent;\n  color: #dc2626;\n  border: 2px solid #dc2626;\n  border-radius: 6px;\n  font-weight: 600;\n  font-size: 0.9rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.clearBtn:hover:not(:disabled) {\n  background: #dc2626;\n  color: white;\n}\n\n.clearBtn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.clearIcon {\n  width: 18px;\n  height: 18px;\n  stroke-width: 2;\n}\n\n/* Security Notice */\n.securityNotice {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  color: #059669;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-align: center;\n}\n\n.securityIcon {\n  width: 16px;\n  height: 16px;\n  stroke-width: 2;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .cartSummary {\n    position: static;\n    margin-top: 2rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .cartSummary {\n    padding: 1.5rem;\n    margin-top: 1.5rem;\n  }\n\n  .title {\n    font-size: 1.25rem;\n    margin-bottom: 1rem;\n  }\n\n  .totalLabel {\n    font-size: 1.1rem;\n  }\n\n  .totalValue {\n    font-size: 1.25rem;\n  }\n\n  .checkoutBtn {\n    padding: 0.875rem 1.25rem;\n    font-size: 0.95rem;\n  }\n\n  .actionButtons {\n    gap: 0.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .cartSummary {\n    padding: 1rem;\n  }\n\n  .summaryRow {\n    margin-bottom: 0.5rem;\n  }\n\n  .label,\n  .value {\n    font-size: 0.9rem;\n  }\n\n  .totalRow {\n    padding: 0.75rem 0;\n  }\n\n  .checkoutBtn {\n    padding: 0.75rem 1rem;\n    font-size: 0.9rem;\n  }\n\n  .clearBtn {\n    padding: 0.625rem 0.875rem;\n    font-size: 0.85rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;;;;AAWA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;;AAiBA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAOA;EACE;;;;;;AAMF;EACE;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/cart/cart.module.css"], "sourcesContent": ["/* Cart Page Styles - Magazine/Editorial Theme */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n  min-height: 80vh;\n}\n\n/* Header */\n.header {\n  text-align: center;\n  margin-bottom: 3rem;\n  padding-bottom: 2rem;\n  border-bottom: 2px solid #f3f4f6;\n}\n\n.title {\n  color: #4a3728;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  line-height: 1.2;\n}\n\n.subtitle {\n  color: #6b5b4d;\n  font-size: 1.1rem;\n  margin: 0;\n}\n\n/* Loading State */\n.loadingContainer {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  color: #6b5b4d;\n}\n\n.loadingSpinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid #f3f4f6;\n  border-top: 3px solid #8b7355;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Empty Cart */\n.emptyCart {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  min-height: 400px;\n  padding: 2rem;\n}\n\n.emptyIcon {\n  width: 120px;\n  height: 120px;\n  color: #d1d5db;\n  margin-bottom: 2rem;\n}\n\n.emptyIcon svg {\n  width: 100%;\n  height: 100%;\n  stroke-width: 1.5;\n}\n\n.emptyTitle {\n  color: #4a3728;\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 0 0 1rem 0;\n}\n\n.emptyMessage {\n  color: #6b5b4d;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  margin: 0 0 2rem 0;\n  max-width: 500px;\n}\n\n.shopNowBtn {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  background: #8b7355;\n  color: white;\n  border-radius: 6px;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 1.1rem;\n  transition: all 0.3s ease;\n}\n\n.shopNowBtn:hover {\n  background: #6d5a47;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);\n}\n\n.shopIcon {\n  width: 20px;\n  height: 20px;\n  stroke-width: 2;\n}\n\n/* Error Message */\n.errorMessage {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 6px;\n  padding: 1rem;\n  margin-bottom: 2rem;\n  color: #dc2626;\n}\n\n.errorIcon {\n  width: 20px;\n  height: 20px;\n  flex-shrink: 0;\n  stroke-width: 2;\n}\n\n/* Cart Content */\n.cartContent {\n  display: grid;\n  grid-template-columns: 1fr 350px;\n  gap: 3rem;\n  margin-bottom: 3rem;\n}\n\n/* Cart Items */\n.cartItems {\n  min-width: 0; /* Allow content to shrink */\n}\n\n.itemsHeader {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.itemsHeader h2 {\n  color: #4a3728;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0;\n}\n\n.continueShoppingLink {\n  color: #8b7355;\n  text-decoration: none;\n  font-weight: 600;\n  transition: color 0.2s ease;\n}\n\n.continueShoppingLink:hover {\n  color: #6d5a47;\n  text-decoration: underline;\n}\n\n.itemsList {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n/* Cart Summary Container */\n.cartSummaryContainer {\n  /* Styles handled by CartSummary component */\n}\n\n/* Additional Actions */\n.additionalActions {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  padding-top: 2rem;\n  border-top: 2px solid #f3f4f6;\n}\n\n.helpSection,\n.shippingInfo {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.helpSection h3,\n.shippingInfo h3 {\n  color: #4a3728;\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0 0 1rem 0;\n}\n\n.helpSection p {\n  color: #6b5b4d;\n  line-height: 1.6;\n  margin: 0;\n}\n\n.contactLink {\n  color: #8b7355;\n  text-decoration: none;\n  font-weight: 600;\n}\n\n.contactLink:hover {\n  text-decoration: underline;\n}\n\n.shippingInfo ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.shippingInfo li {\n  color: #6b5b4d;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e5e7eb;\n  position: relative;\n  padding-left: 1.5rem;\n}\n\n.shippingInfo li:last-child {\n  border-bottom: none;\n}\n\n.shippingInfo li::before {\n  content: '✓';\n  position: absolute;\n  left: 0;\n  color: #059669;\n  font-weight: bold;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .cartContent {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .container {\n    padding: 1rem;\n  }\n\n  .header {\n    margin-bottom: 2rem;\n    padding-bottom: 1.5rem;\n  }\n\n  .title {\n    font-size: 2rem;\n  }\n\n  .subtitle {\n    font-size: 1rem;\n  }\n\n  .cartContent {\n    gap: 1.5rem;\n  }\n\n  .additionalActions {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .helpSection,\n  .shippingInfo {\n    padding: 1rem;\n  }\n\n  .emptyIcon {\n    width: 80px;\n    height: 80px;\n  }\n\n  .emptyTitle {\n    font-size: 1.5rem;\n  }\n\n  .emptyMessage {\n    font-size: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 0.5rem;\n  }\n\n  .title {\n    font-size: 1.75rem;\n  }\n\n  .itemsHeader {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n\n  .shopNowBtn {\n    padding: 0.875rem 1.5rem;\n    font-size: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAYA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;EACE;;;;;;AAMF;EACE;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA", "debugId": null}}]}